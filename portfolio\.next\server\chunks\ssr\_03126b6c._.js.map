{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover?: boolean;\n  gradient?: boolean;\n  onClick?: () => void;\n}\n\nexport const Card: React.FC<CardProps> = ({ \n  children, \n  className = '', \n  hover = true, \n  gradient = false,\n  onClick \n}) => {\n  const baseClasses = `\n    rounded-lg border flex flex-col gap-6 shadow-sm py-6 \n    text-[var(--text-primary)] group relative \n    border-[var(--card-border)] bg-[var(--card-background)]\n    transition-all duration-300 overflow-hidden\n  `;\n  \n  const hoverClasses = hover ? 'hover:bg-[var(--hover-background)] cursor-pointer' : '';\n  const gradientClasses = gradient ? `\n    before:absolute before:-inset-[1px] before:bg-gradient-to-r \n    before:from-blue-500/20 before:via-cyan-500/20 before:to-transparent \n    before:opacity-0 before:group-hover:opacity-100 before:transition \n    before:duration-500 before:blur-sm\n  ` : '';\n\n  return (\n    <div \n      className={`${baseClasses} ${hoverClasses} ${gradientClasses} ${className}`}\n      onClick={onClick}\n    >\n      {gradient && <div className=\"absolute left-0 top-0 h-full w-1 bg-gradient-to-b from-blue-500/20 via-cyan-500/20 to-transparent\"></div>}\n      {children}\n    </div>\n  );\n};\n\ninterface CardHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardHeader: React.FC<CardHeaderProps> = ({ children, className = '' }) => {\n  return (\n    <div className={`@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 pb-4 ${className}`}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardContent: React.FC<CardContentProps> = ({ children, className = '' }) => {\n  return (\n    <div className={`px-6 pt-0 pb-4 ${className}`}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardTitleProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardTitle: React.FC<CardTitleProps> = ({ children, className = '' }) => {\n  return (\n    <div className={`font-semibold text-lg text-[var(--text-primary)] ${className}`}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardDescriptionProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardDescription: React.FC<CardDescriptionProps> = ({ children, className = '' }) => {\n  return (\n    <div className={`text-sm text-[var(--text-muted)] ${className}`}>\n      {children}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;;;AAUO,MAAM,OAA4B,CAAC,EACxC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,IAAI,EACZ,WAAW,KAAK,EAChB,OAAO,EACR;IACC,MAAM,cAAc,CAAC;;;;;EAKrB,CAAC;IAED,MAAM,eAAe,QAAQ,sDAAsD;IACnF,MAAM,kBAAkB,WAAW,CAAC;;;;;EAKpC,CAAC,GAAG;IAEJ,qBACE,8OAAC;QACC,WAAW,GAAG,YAAY,CAAC,EAAE,aAAa,CAAC,EAAE,gBAAgB,CAAC,EAAE,WAAW;QAC3E,SAAS;;YAER,0BAAY,8OAAC;gBAAI,WAAU;;;;;;YAC3B;;;;;;;AAGP;AAOO,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IAChF,qBACE,8OAAC;QAAI,WAAW,CAAC,8FAA8F,EAAE,WAAW;kBACzH;;;;;;AAGP;AAOO,MAAM,cAA0C,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IAClF,qBACE,8OAAC;QAAI,WAAW,CAAC,eAAe,EAAE,WAAW;kBAC1C;;;;;;AAGP;AAOO,MAAM,YAAsC,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IAC9E,qBACE,8OAAC;QAAI,WAAW,CAAC,iDAAiD,EAAE,WAAW;kBAC5E;;;;;;AAGP;AAOO,MAAM,kBAAkD,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IAC1F,qBACE,8OAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC5D;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/ui/Badge.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface BadgeProps {\n  children: React.ReactNode;\n  variant?: 'default' | 'secondary' | 'outline' | 'status';\n  className?: string;\n  href?: string;\n  target?: string;\n  rel?: string;\n}\n\nexport const Badge: React.FC<BadgeProps> = ({ \n  children, \n  variant = 'default', \n  className = '',\n  href,\n  target,\n  rel\n}) => {\n  const baseClasses = `\n    inline-flex items-center justify-center rounded-md border px-2 py-0.5 \n    text-xs font-medium w-fit whitespace-nowrap shrink-0 gap-1 \n    transition-colors overflow-hidden\n  `;\n\n  const variantClasses = {\n    default: `\n      bg-[var(--hover-background)] border-[var(--card-border)] \n      hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] \n      text-[var(--text-primary)]\n    `,\n    secondary: `\n      bg-blue-500/10 text-blue-400 hover:bg-blue-500/20 \n      border-transparent\n    `,\n    outline: `\n      border-[var(--card-border)] bg-transparent \n      hover:bg-[var(--hover-background)] text-[var(--text-secondary)]\n    `,\n    status: `\n      bg-green-500/10 text-green-400 hover:bg-green-500/20 \n      border-green-500/20\n    `\n  };\n\n  const classes = `${baseClasses} ${variantClasses[variant]} ${className}`;\n\n  if (href) {\n    return (\n      <a \n        href={href} \n        target={target} \n        rel={rel}\n        className={`${classes} hover:no-underline`}\n      >\n        {children}\n      </a>\n    );\n  }\n\n  return (\n    <span className={classes}>\n      {children}\n    </span>\n  );\n};\n\ninterface BadgeGroupProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const BadgeGroup: React.FC<BadgeGroupProps> = ({ children, className = '' }) => {\n  return (\n    <div className={`flex flex-wrap gap-2 ${className}`}>\n      {children}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;AAWO,MAAM,QAA8B,CAAC,EAC1C,QAAQ,EACR,UAAU,SAAS,EACnB,YAAY,EAAE,EACd,IAAI,EACJ,MAAM,EACN,GAAG,EACJ;IACC,MAAM,cAAc,CAAC;;;;EAIrB,CAAC;IAED,MAAM,iBAAiB;QACrB,SAAS,CAAC;;;;IAIV,CAAC;QACD,WAAW,CAAC;;;IAGZ,CAAC;QACD,SAAS,CAAC;;;IAGV,CAAC;QACD,QAAQ,CAAC;;;IAGT,CAAC;IACH;IAEA,MAAM,UAAU,GAAG,YAAY,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW;IAExE,IAAI,MAAM;QACR,qBACE,8OAAC;YACC,MAAM;YACN,QAAQ;YACR,KAAK;YACL,WAAW,GAAG,QAAQ,mBAAmB,CAAC;sBAEzC;;;;;;IAGP;IAEA,qBACE,8OAAC;QAAK,WAAW;kBACd;;;;;;AAGP;AAOO,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IAChF,qBACE,8OAAC;QAAI,WAAW,CAAC,qBAAqB,EAAE,WAAW;kBAChD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface ButtonProps {\n  children: React.ReactNode;\n  variant?: 'default' | 'ghost' | 'outline' | 'link';\n  size?: 'sm' | 'md' | 'lg' | 'icon';\n  className?: string;\n  onClick?: () => void;\n  href?: string;\n  target?: string;\n  rel?: string;\n  disabled?: boolean;\n}\n\nexport const Button: React.FC<ButtonProps> = ({ \n  children, \n  variant = 'default', \n  size = 'md',\n  className = '',\n  onClick,\n  href,\n  target,\n  rel,\n  disabled = false\n}) => {\n  const baseClasses = `\n    inline-flex items-center justify-center rounded-lg font-medium \n    transition-colors focus-visible:outline-none focus-visible:ring-2 \n    focus-visible:ring-ring focus-visible:ring-offset-2 \n    disabled:pointer-events-none disabled:opacity-50\n  `;\n\n  const variantClasses = {\n    default: `\n      bg-[var(--text-primary)] text-[var(--background)] \n      hover:bg-[var(--text-secondary)]\n    `,\n    ghost: `\n      hover:bg-[var(--hover-background)] hover:text-[var(--text-primary)]\n      text-[var(--text-secondary)]\n    `,\n    outline: `\n      border border-[var(--card-border)] bg-transparent \n      hover:bg-[var(--hover-background)] hover:text-[var(--text-primary)]\n      text-[var(--text-secondary)]\n    `,\n    link: `\n      text-[var(--text-primary)] underline-offset-4 hover:underline\n      bg-transparent\n    `\n  };\n\n  const sizeClasses = {\n    sm: 'h-8 px-3 text-xs',\n    md: 'h-10 px-4 py-2',\n    lg: 'h-11 px-8',\n    icon: 'h-10 w-10'\n  };\n\n  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;\n\n  if (href) {\n    return (\n      <a \n        href={href} \n        target={target} \n        rel={rel}\n        className={`${classes} no-underline`}\n      >\n        {children}\n      </a>\n    );\n  }\n\n  return (\n    <button \n      className={classes}\n      onClick={onClick}\n      disabled={disabled}\n    >\n      {children}\n    </button>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAcO,MAAM,SAAgC,CAAC,EAC5C,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,OAAO,EACP,IAAI,EACJ,MAAM,EACN,GAAG,EACH,WAAW,KAAK,EACjB;IACC,MAAM,cAAc,CAAC;;;;;EAKrB,CAAC;IAED,MAAM,iBAAiB;QACrB,SAAS,CAAC;;;IAGV,CAAC;QACD,OAAO,CAAC;;;IAGR,CAAC;QACD,SAAS,CAAC;;;;IAIV,CAAC;QACD,MAAM,CAAC;;;IAGP,CAAC;IACH;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,MAAM,UAAU,GAAG,YAAY,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;IAE7F,IAAI,MAAM;QACR,qBACE,8OAAC;YACC,MAAM;YACN,QAAQ;YACR,KAAK;YACL,WAAW,GAAG,QAAQ,aAAa,CAAC;sBAEnC;;;;;;IAGP;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,SAAS;QACT,UAAU;kBAET;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/ui/Timeline.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface TimelineProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const Timeline: React.FC<TimelineProps> = ({ children, className = '' }) => {\n  return (\n    <div className={`relative mx-auto w-full max-w-4xl h-fit ${className}`}>\n      {/* Timeline line */}\n      <div className=\"absolute top-3 -left-4 md:-left-20 hidden md:block\">\n        <div className=\"border-neutral-200 ml-[27px] flex h-4 w-4 items-center justify-center rounded-full border shadow-sm\"\n             style={{boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px'}}>\n          <div className=\"h-2 w-2 rounded-full border border-neutral-300 bg-white\" \n               style={{backgroundColor: 'rgb(16, 185, 129)', borderColor: 'rgb(5, 150, 105)'}}></div>\n        </div>\n        <svg viewBox=\"0 0 20 3140\" width=\"20\" height=\"3140\" className=\"ml-4 block\" aria-hidden=\"true\">\n          <path d=\"M 1 0V -36 l 18 24 V 2512 l -18 24V 3140\" fill=\"none\" stroke=\"#9091A0\" strokeOpacity=\"0.16\"></path>\n          <path d=\"M 1 0V -36 l 18 24 V 2512 l -18 24V 3140\" fill=\"none\" stroke=\"url(#gradient)\" strokeWidth=\"1.25\" className=\"motion-reduce:hidden\"></path>\n          <defs>\n            <linearGradient id=\"gradient\" gradientUnits=\"userSpaceOnUse\" x1=\"0\" x2=\"0\" y1=\"2844.2869344506103\" y2=\"2140.7415509546313\">\n              <stop stopColor=\"#18CCFC\" stopOpacity=\"0\"></stop>\n              <stop stopColor=\"#18CCFC\"></stop>\n              <stop offset=\"0.325\" stopColor=\"#6344F5\"></stop>\n              <stop offset=\"1\" stopColor=\"#AE48FF\" stopOpacity=\"0\"></stop>\n            </linearGradient>\n          </defs>\n        </svg>\n      </div>\n      <div className=\"space-y-6\">\n        {children}\n      </div>\n    </div>\n  );\n};\n\ninterface TimelineItemProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const TimelineItem: React.FC<TimelineItemProps> = ({ children, className = '' }) => {\n  return (\n    <div className={`relative ${className}`} style={{opacity: 1, transform: 'none'}}>\n      {children}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;AAOO,MAAM,WAAoC,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IAC5E,qBACE,8OAAC;QAAI,WAAW,CAAC,wCAAwC,EAAE,WAAW;;0BAEpE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBACV,OAAO;4BAAC,WAAW;wBAAiC;kCACvD,cAAA,8OAAC;4BAAI,WAAU;4BACV,OAAO;gCAAC,iBAAiB;gCAAqB,aAAa;4BAAkB;;;;;;;;;;;kCAEpF,8OAAC;wBAAI,SAAQ;wBAAc,OAAM;wBAAK,QAAO;wBAAO,WAAU;wBAAa,eAAY;;0CACrF,8OAAC;gCAAK,GAAE;gCAA2C,MAAK;gCAAO,QAAO;gCAAU,eAAc;;;;;;0CAC9F,8OAAC;gCAAK,GAAE;gCAA2C,MAAK;gCAAO,QAAO;gCAAiB,aAAY;gCAAO,WAAU;;;;;;0CACpH,8OAAC;0CACC,cAAA,8OAAC;oCAAe,IAAG;oCAAW,eAAc;oCAAiB,IAAG;oCAAI,IAAG;oCAAI,IAAG;oCAAqB,IAAG;;sDACpG,8OAAC;4CAAK,WAAU;4CAAU,aAAY;;;;;;sDACtC,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;4CAAK,QAAO;4CAAQ,WAAU;;;;;;sDAC/B,8OAAC;4CAAK,QAAO;4CAAI,WAAU;4CAAU,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAKzD,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;AAOO,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IACpF,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;QAAE,OAAO;YAAC,SAAS;YAAG,WAAW;QAAM;kBAC3E;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/ui/Tabs.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\ninterface TabsProps {\n  children: React.ReactNode;\n  defaultValue: string;\n  className?: string;\n}\n\ninterface TabsContextType {\n  activeTab: string;\n  setActiveTab: (value: string) => void;\n}\n\nconst TabsContext = React.createContext<TabsContextType | undefined>(undefined);\n\nexport const Tabs: React.FC<TabsProps> = ({ children, defaultValue, className = '' }) => {\n  const [activeTab, setActiveTab] = useState(defaultValue);\n\n  return (\n    <TabsContext.Provider value={{ activeTab, setActiveTab }}>\n      <div className={`flex flex-col gap-2 w-full ${className}`}>\n        {children}\n      </div>\n    </TabsContext.Provider>\n  );\n};\n\ninterface TabsListProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const TabsList: React.FC<TabsListProps> = ({ children, className = '' }) => {\n  return (\n    <div className={`\n      text-muted-foreground h-9 items-center justify-center rounded-lg p-[3px] \n      grid w-full grid-cols-2 mb-8 bg-[var(--card-background)] \n      border-[var(--card-border)] border\n      ${className}\n    `}>\n      {children}\n    </div>\n  );\n};\n\ninterface TabsTriggerProps {\n  children: React.ReactNode;\n  value: string;\n  className?: string;\n}\n\nexport const TabsTrigger: React.FC<TabsTriggerProps> = ({ children, value, className = '' }) => {\n  const context = React.useContext(TabsContext);\n  if (!context) {\n    throw new Error('TabsTrigger must be used within Tabs');\n  }\n\n  const { activeTab, setActiveTab } = context;\n  const isActive = activeTab === value;\n\n  return (\n    <button\n      type=\"button\"\n      onClick={() => setActiveTab(value)}\n      className={`\n        inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 \n        rounded-md border border-transparent px-2 py-1 text-sm font-medium \n        whitespace-nowrap transition-[color,box-shadow] \n        disabled:pointer-events-none disabled:opacity-50\n        ${isActive \n          ? 'bg-[var(--hover-background)] text-[var(--text-primary)] shadow-sm' \n          : 'text-[var(--text-secondary)] hover:text-[var(--text-primary)]'\n        }\n        ${className}\n      `}\n    >\n      {children}\n    </button>\n  );\n};\n\ninterface TabsContentProps {\n  children: React.ReactNode;\n  value: string;\n  className?: string;\n}\n\nexport const TabsContent: React.FC<TabsContentProps> = ({ children, value, className = '' }) => {\n  const context = React.useContext(TabsContext);\n  if (!context) {\n    throw new Error('TabsContent must be used within Tabs');\n  }\n\n  const { activeTab } = context;\n  \n  if (activeTab !== value) {\n    return null;\n  }\n\n  return (\n    <div className={`flex-1 outline-none mt-0 ${className}`}>\n      {children}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;;;AAaA,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,aAAa,CAA8B;AAE9D,MAAM,OAA4B,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,EAAE;IAClF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAW;QAAa;kBACrD,cAAA,8OAAC;YAAI,WAAW,CAAC,2BAA2B,EAAE,WAAW;sBACtD;;;;;;;;;;;AAIT;AAOO,MAAM,WAAoC,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IAC5E,qBACE,8OAAC;QAAI,WAAW,CAAC;;;;MAIf,EAAE,UAAU;IACd,CAAC;kBACE;;;;;;AAGP;AAQO,MAAM,cAA0C,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;IACzF,MAAM,UAAU,qMAAA,CAAA,UAAK,CAAC,UAAU,CAAC;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG;IACpC,MAAM,WAAW,cAAc;IAE/B,qBACE,8OAAC;QACC,MAAK;QACL,SAAS,IAAM,aAAa;QAC5B,WAAW,CAAC;;;;;QAKV,EAAE,WACE,sEACA,gEACH;QACD,EAAE,UAAU;MACd,CAAC;kBAEA;;;;;;AAGP;AAQO,MAAM,cAA0C,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;IACzF,MAAM,UAAU,qMAAA,CAAA,UAAK,CAAC,UAAU,CAAC;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,SAAS,EAAE,GAAG;IAEtB,IAAI,cAAc,OAAO;QACvB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,yBAAyB,EAAE,WAAW;kBACpD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/ui/index.ts"], "sourcesContent": ["export { Card, <PERSON><PERSON><PERSON>er, CardContent, CardTitle, CardDescription } from './Card';\nexport { Badge, BadgeGroup } from './Badge';\nexport { Button } from './Button';\nexport { Timeline, TimelineItem } from './Timeline';\nexport { Ta<PERSON>, Tabs<PERSON>ist, TabsTrigger, TabsContent } from './Tabs';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 537, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/Hero.tsx"], "sourcesContent": ["import React from 'react';\nimport { G<PERSON><PERSON>, Linkedin, Mail, Phone } from 'lucide-react';\nimport { Button } from './ui';\n\nexport const Hero: React.FC = () => {\n  return (\n    <section>\n      <div className=\"flex justify-between items-start mb-8 pt-20\">\n        {/* Profile Image */}\n        <div className=\"relative w-[128px] h-[128px] rounded-2xl overflow-hidden\">\n          <div className=\"h-28 w-28 rounded-3xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-4xl font-bold\">\n            SR\n          </div>\n        </div>\n        \n        {/* Social Links */}\n        <div className=\"flex items-center gap-6\">\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            href=\"https://github.com/dorddis\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"w-10 h-10 hover:bg-[var(--hover-background)] rounded-lg transition-colors\"\n          >\n            <Github size={20} />\n          </Button>\n          \n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            href=\"https://linkedin.com/in/dorddis\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"w-10 h-10 hover:bg-[var(--hover-background)] rounded-lg transition-colors\"\n          >\n            <Linkedin size={20} />\n          </Button>\n          \n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            href=\"mailto:<EMAIL>\"\n            className=\"w-10 h-10 hover:bg-[var(--hover-background)] rounded-lg transition-colors\"\n          >\n            <Mail size={20} />\n          </Button>\n          \n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            href=\"tel:+919356252711\"\n            className=\"w-10 h-10 hover:bg-[var(--hover-background)] rounded-lg transition-colors\"\n          >\n            <Phone size={20} />\n          </Button>\n        </div>\n      </div>\n      \n      {/* Hero Content */}\n      <h1 className=\"text-[32px] leading-none font-medium text-[var(--text-primary)] mb-2\">\n        Hi, I'm Siddharth\n      </h1>\n      \n      <p className=\"text-[var(--text-secondary)] text-base mb-4\">\n        22, Mumbai | Software Engineer | AI/ML, Python, Backend, Cloud\n      </p>\n      \n      <p className=\"text-[var(--text-secondary)] max-w-xl\">\n        I'm a Software Engineer crafting cutting-edge AI/ML solutions and scalable backend systems. \n        From building distributed web scraping platforms to developing interactive fitness analytics, \n        I turn complex technical challenges into user-friendly experiences. Available for U.S. remote roles \n        with 9 AM EST - 6 PM IST overlap.\n      </p>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AACA;AAAA;;;;AAEO,MAAM,OAAiB;IAC5B,qBACE,8OAAC;;0BACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAAqI;;;;;;;;;;;kCAMtJ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;0CAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,MAAM;;;;;;;;;;;0CAGhB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;0CAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,MAAM;;;;;;;;;;;0CAGlB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;;;;;;0CAGd,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAMnB,8OAAC;gBAAG,WAAU;0BAAuE;;;;;;0BAIrF,8OAAC;gBAAE,WAAU;0BAA8C;;;;;;0BAI3D,8OAAC;gBAAE,WAAU;0BAAwC;;;;;;;;;;;;AAQ3D", "debugId": null}}, {"offset": {"line": 695, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/Experience.tsx"], "sourcesContent": ["import React from 'react';\nimport { Building2, Briefcase } from 'lucide-react';\nimport { <PERSON>, Card<PERSON>eader, CardContent, CardTitle, CardDescription, Badge, BadgeGroup, Timeline, TimelineItem } from './ui';\n\nconst experiences = [\n  {\n    id: 1,\n    title: \"Lead Developer (Full Stack & Cloud Infrastructure)\",\n    company: \"Undisclosed Client (Electoral Platform)\",\n    duration: \"Nov 2024 - Feb 2025\",\n    location: \"Mumbai, India\",\n    status: \"Current\",\n    description: \"Pioneered India's first fully streamlined electoral-sampling platform\",\n    achievements: [\n      \"Led end-to-end R&D and full-stack implementation to eliminate manual data collection\",\n      \"Built distributed web scraping system on AWS EC2 with S3 for data storage\",\n      \"Reduced data collection time by 9600 man-days worth of work\",\n      \"Engineered high-throughput data extraction achieving 50x improvement (4000 per hour)\",\n      \"Built advanced image processing modules with 97% accuracy in data extraction\"\n    ],\n    technologies: [\"Python\", \"AWS EC2\", \"AWS S3\", \"Selenium\", \"Tesseract\", \"EasyOCR\", \"Multi-threading\"],\n    links: [\n      { label: \"Platform\", url: \"https://edownloaders.com\", icon: \"globe\" }\n    ],\n    icon: Building2,\n    color: \"blue\"\n  },\n  {\n    id: 2,\n    title: \"Software Intern (Business Development Automation)\",\n    company: \"Viven Ediversity Pvt. Ltd.\",\n    duration: \"June 2024 - Jan 2025\",\n    location: \"Thane, India\",\n    status: \"Past\",\n    description: \"Executed comprehensive website testing and business automation solutions\",\n    achievements: [\n      \"Launched 60 courses with 100% positive response rate\",\n      \"Integrated WhatsApp Business API saving 3+ hours daily\",\n      \"Automated 500+ warm emails daily through custom solutions\",\n      \"Implemented SEO best practices and endpoint integrations\"\n    ],\n    technologies: [\"Google Sheets Apps Script\", \"Pabbly\", \"WhatsApp Business API\", \"SEO\"],\n    links: [],\n    icon: Briefcase,\n    color: \"green\"\n  }\n];\n\nexport const Experience: React.FC = () => {\n  return (\n    <section className=\"py-20\">\n      <h2 className=\"text-2xl font-medium mb-3 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text\">\n        Experience\n      </h2>\n      <p className=\"text-[var(--text-muted)] mb-8\">\n        Here's a timeline of my professional journey, showcasing my roles and contributions in \n        full-stack development, cloud infrastructure, and business automation.\n      </p>\n      \n      <Timeline>\n        {experiences.map((exp) => (\n          <TimelineItem key={exp.id}>\n            <Card gradient hover>\n              <CardHeader>\n                <div className=\"flex flex-col sm:flex-row items-start gap-4\">\n                  <div className=\"p-2 rounded-lg bg-[var(--hover-background)]\">\n                    <exp.icon className={`w-5 h-5 text-${exp.color}-400`} />\n                  </div>\n                  \n                  <div className=\"space-y-1.5 flex-grow w-full\">\n                    <div className=\"flex flex-col sm:flex-row sm:items-center justify-between gap-2\">\n                      <div className=\"flex flex-wrap items-center gap-2\">\n                        <CardTitle>{exp.title}</CardTitle>\n                        <span className=\"text-sm text-[var(--text-muted)]\">•</span>\n                        <span className=\"text-[var(--text-muted)]\">{exp.company}</span>\n                      </div>\n                      \n                      <div className=\"flex flex-wrap gap-2\">\n                        {exp.links.map((link, index) => (\n                          <Badge \n                            key={index}\n                            href={link.url}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            variant=\"outline\"\n                          >\n                            {link.label}\n                          </Badge>\n                        ))}\n                      </div>\n                    </div>\n                    \n                    <div className=\"text-sm text-[var(--text-muted)]\">{exp.duration}</div>\n                    <div className=\"text-sm text-[var(--text-muted)]\">{exp.location}</div>\n                    <CardDescription>{exp.description}</CardDescription>\n                    \n                    {/* Achievements */}\n                    <div className=\"mt-3\">\n                      <h4 className=\"text-sm font-medium text-[var(--text-primary)] mb-2\">Key Achievements:</h4>\n                      <ul className=\"text-sm text-[var(--text-muted)] space-y-1\">\n                        {exp.achievements.map((achievement, index) => (\n                          <li key={index} className=\"flex items-start gap-2\">\n                            <span className=\"text-blue-400 mt-1\">•</span>\n                            <span>{achievement}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  </div>\n                </div>\n              </CardHeader>\n              \n              <CardContent>\n                <BadgeGroup>\n                  {exp.technologies.map((tech, index) => (\n                    <Badge key={index}>{tech}</Badge>\n                  ))}\n                </BadgeGroup>\n              </CardContent>\n            </Card>\n          </TimelineItem>\n        ))}\n      </Timeline>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;AAAA;AACA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAU;YAAW;YAAU;YAAY;YAAa;YAAW;SAAkB;QACpG,OAAO;YACL;gBAAE,OAAO;gBAAY,KAAK;gBAA4B,MAAM;YAAQ;SACrE;QACD,MAAM,gNAAA,CAAA,YAAS;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAA6B;YAAU;YAAyB;SAAM;QACrF,OAAO,EAAE;QACT,MAAM,4MAAA,CAAA,YAAS;QACf,OAAO;IACT;CACD;AAEM,MAAM,aAAuB;IAClC,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAG,WAAU;0BAAqG;;;;;;0BAGnH,8OAAC;gBAAE,WAAU;0BAAgC;;;;;;0BAK7C,8OAAC,oIAAA,CAAA,WAAQ;0BACN,YAAY,GAAG,CAAC,CAAC,oBAChB,8OAAC,oIAAA,CAAA,eAAY;kCACX,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,QAAQ;4BAAC,KAAK;;8CAClB,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,IAAI,IAAI;oDAAC,WAAW,CAAC,aAAa,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC;;;;;;;;;;;0DAGtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gIAAA,CAAA,YAAS;kFAAE,IAAI,KAAK;;;;;;kFACrB,8OAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,8OAAC;wEAAK,WAAU;kFAA4B,IAAI,OAAO;;;;;;;;;;;;0EAGzD,8OAAC;gEAAI,WAAU;0EACZ,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,iIAAA,CAAA,QAAK;wEAEJ,MAAM,KAAK,GAAG;wEACd,QAAO;wEACP,KAAI;wEACJ,SAAQ;kFAEP,KAAK,KAAK;uEANN;;;;;;;;;;;;;;;;kEAYb,8OAAC;wDAAI,WAAU;kEAAoC,IAAI,QAAQ;;;;;;kEAC/D,8OAAC;wDAAI,WAAU;kEAAoC,IAAI,QAAQ;;;;;;kEAC/D,8OAAC,gIAAA,CAAA,kBAAe;kEAAE,IAAI,WAAW;;;;;;kEAGjC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAsD;;;;;;0EACpE,8OAAC;gEAAG,WAAU;0EACX,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBAClC,8OAAC;wEAAe,WAAU;;0FACxB,8OAAC;gFAAK,WAAU;0FAAqB;;;;;;0FACrC,8OAAC;0FAAM;;;;;;;uEAFA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAWrB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,iIAAA,CAAA,aAAU;kDACR,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC3B,8OAAC,iIAAA,CAAA,QAAK;0DAAc;+CAAR;;;;;;;;;;;;;;;;;;;;;uBAtDH,IAAI,EAAE;;;;;;;;;;;;;;;;AAgEnC", "debugId": null}}, {"offset": {"line": 1010, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/Projects.tsx"], "sourcesContent": ["import React from 'react';\nimport { ExternalLink, Github } from 'lucide-react';\nimport { <PERSON>, Card<PERSON>eader, CardContent, CardTitle, CardDescription, Badge, BadgeGroup, Tabs, TabsList, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON>bsContent } from './ui';\n\nconst personalProjects = [\n  {\n    id: 1,\n    title: \"Gymzy AI Fitness Analytics & Social Media Platform\",\n    description: \"Interactive AI-based fitness analytics and workout logging platform with social integration\",\n    status: \"Live\",\n    statusColor: \"green\",\n    features: [\n      \"Real-time AI-supported workout logging\",\n      \"Personalized muscle activation analytics\", \n      \"Interactive SVG visualizations\",\n      \"40% improvement in user engagement\"\n    ],\n    technologies: [\"Next.js\", \"TypeScript\", \"Node.js\", \"AI Integration\"],\n    links: [\n      { label: \"Live Demo\", url: \"https://gymzy.vercel.app\", icon: ExternalLink },\n      { label: \"GitHub\", url: \"https://github.com/dorddis/gymzy\", icon: Github }\n    ],\n    gradient: \"from-purple-600/20 via-pink-600/20 to-red-600/20\"\n  },\n  {\n    id: 2,\n    title: \"EggyPro E-commerce Platform\",\n    description: \"Modern e-commerce frontend with AI-powered customer support\",\n    status: \"Live\",\n    statusColor: \"green\",\n    features: [\n      \"AI-powered FAQ assistant using Google's Gemini AI\",\n      \"90% reduction in customer support response time\",\n      \"95% mobile compatibility\",\n      \"Sub-1 second page load times\"\n    ],\n    technologies: [\"Next.js 15\", \"TypeScript\", \"Google Gemini AI\", \"Genkit\"],\n    links: [\n      { label: \"Live Demo\", url: \"https://eggypro.com\", icon: ExternalLink },\n      { label: \"GitHub\", url: \"https://github.com/dorddis/eggypro\", icon: Github }\n    ],\n    gradient: \"from-cyan-600/20 via-blue-600/20 to-indigo-600/20\"\n  },\n  {\n    id: 3,\n    title: \"AI-assisted Coding and Debugging\",\n    description: \"Accelerated software development through innovative AI model implementation\",\n    status: \"Ongoing\",\n    statusColor: \"blue\",\n    features: [\n      \"50% reduction in debugging time\",\n      \"150% improvement in development efficiency\",\n      \"Implementation of o1, o3, and o4-mini models\",\n      \"Advanced prompt engineering techniques\"\n    ],\n    technologies: [\"AI Models (o1, o3, o4-mini)\", \"Prompt Engineering\", \"Aider CLI\"],\n    links: [\n      { label: \"GitHub\", url: \"https://github.com/dorddis\", icon: Github }\n    ],\n    gradient: \"from-green-600/20 via-emerald-600/20 to-teal-600/20\"\n  }\n];\n\nconst professionalProjects = [\n  {\n    id: 1,\n    title: \"India's First Electoral-Sampling Platform\",\n    description: \"Fully streamlined platform for electoral data collection and analysis\",\n    status: \"Deployed\",\n    statusColor: \"green\",\n    features: [\n      \"End-to-end R&D and full-stack implementation\",\n      \"Distributed web scraping system on AWS\",\n      \"9600 man-days of work automation\",\n      \"97% accuracy in data extraction\"\n    ],\n    technologies: [\"Python\", \"AWS EC2\", \"AWS S3\", \"Selenium Hub\", \"OCR\"],\n    links: [\n      { label: \"Platform\", url: \"https://edownloaders.com\", icon: ExternalLink }\n    ],\n    gradient: \"from-blue-600/20 via-indigo-600/20 to-purple-600/20\"\n  }\n];\n\nexport const Projects: React.FC = () => {\n  return (\n    <section className=\"py-20\">\n      <h2 className=\"text-2xl font-medium mb-3 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text\">\n        Projects\n      </h2>\n      <p className=\"text-[var(--text-muted)] mb-8\">\n        A collection of my work spanning from AI/ML applications to full-stack projects, \n        both personal and professional.\n      </p>\n      \n      <Tabs defaultValue=\"personal\">\n        <TabsList>\n          <TabsTrigger value=\"personal\">Personal Projects</TabsTrigger>\n          <TabsTrigger value=\"professional\">Professional Work</TabsTrigger>\n        </TabsList>\n        \n        <TabsContent value=\"personal\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 min-h-[400px]\">\n            {personalProjects.map((project) => (\n              <ProjectCard key={project.id} project={project} />\n            ))}\n          </div>\n        </TabsContent>\n        \n        <TabsContent value=\"professional\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 min-h-[400px]\">\n            {professionalProjects.map((project) => (\n              <ProjectCard key={project.id} project={project} />\n            ))}\n          </div>\n        </TabsContent>\n      </Tabs>\n    </section>\n  );\n};\n\ninterface ProjectCardProps {\n  project: {\n    title: string;\n    description: string;\n    status: string;\n    statusColor: string;\n    features: string[];\n    technologies: string[];\n    links: Array<{ label: string; url: string; icon: any }>;\n    gradient: string;\n  };\n}\n\nconst ProjectCard: React.FC<ProjectCardProps> = ({ project }) => {\n  return (\n    <Card className=\"group relative\" hover gradient>\n      <div className={`absolute -inset-[1px] bg-gradient-to-r ${project.gradient} opacity-0 group-hover:opacity-100 transition duration-500 blur-sm`}></div>\n      \n      <div className=\"relative\">\n        <CardHeader>\n          <div className=\"flex items-start justify-between\">\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center gap-2\">\n                <CardTitle className=\"group-hover:text-[var(--text-primary)] transition-colors\">\n                  {project.title}\n                </CardTitle>\n                <ExternalLink className=\"w-4 h-4 opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:translate-x-1\" />\n              </div>\n              <CardDescription className=\"line-clamp-2\">\n                {project.description}\n              </CardDescription>\n            </div>\n            \n            <Badge variant={project.statusColor === 'green' ? 'status' : 'secondary'}>\n              {project.status}\n            </Badge>\n          </div>\n          \n          {/* Features */}\n          <div className=\"mt-4\">\n            <h4 className=\"text-sm font-medium text-[var(--text-primary)] mb-2\">Key Features:</h4>\n            <ul className=\"text-sm text-[var(--text-muted)] space-y-1\">\n              {project.features.map((feature, index) => (\n                <li key={index} className=\"flex items-start gap-2\">\n                  <span className=\"text-blue-400 mt-1\">•</span>\n                  <span>{feature}</span>\n                </li>\n              ))}\n            </ul>\n          </div>\n          \n          {/* Links */}\n          {project.links.length > 0 && (\n            <div className=\"flex flex-wrap gap-2 mt-4\">\n              {project.links.map((link, index) => (\n                <Badge \n                  key={index}\n                  href={link.url}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  variant=\"outline\"\n                  className=\"flex items-center gap-1.5\"\n                >\n                  <link.icon className=\"w-3 h-3\" />\n                  <span className=\"text-xs\">{link.label}</span>\n                </Badge>\n              ))}\n            </div>\n          )}\n        </CardHeader>\n        \n        <CardContent>\n          <BadgeGroup>\n            {project.technologies.map((tech, index) => (\n              <Badge key={index}>{tech}</Badge>\n            ))}\n          </BadgeGroup>\n        </CardContent>\n      </div>\n    </Card>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;AAAA;AACA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,mBAAmB;IACvB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAW;YAAc;YAAW;SAAiB;QACpE,OAAO;YACL;gBAAE,OAAO;gBAAa,KAAK;gBAA4B,MAAM,sNAAA,CAAA,eAAY;YAAC;YAC1E;gBAAE,OAAO;gBAAU,KAAK;gBAAoC,MAAM,sMAAA,CAAA,SAAM;YAAC;SAC1E;QACD,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAc;YAAc;YAAoB;SAAS;QACxE,OAAO;YACL;gBAAE,OAAO;gBAAa,KAAK;gBAAuB,MAAM,sNAAA,CAAA,eAAY;YAAC;YACrE;gBAAE,OAAO;gBAAU,KAAK;gBAAsC,MAAM,sMAAA,CAAA,SAAM;YAAC;SAC5E;QACD,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAA+B;YAAsB;SAAY;QAChF,OAAO;YACL;gBAAE,OAAO;gBAAU,KAAK;gBAA8B,MAAM,sMAAA,CAAA,SAAM;YAAC;SACpE;QACD,UAAU;IACZ;CACD;AAED,MAAM,uBAAuB;IAC3B;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAU;YAAW;YAAU;YAAgB;SAAM;QACpE,OAAO;YACL;gBAAE,OAAO;gBAAY,KAAK;gBAA4B,MAAM,sNAAA,CAAA,eAAY;YAAC;SAC1E;QACD,UAAU;IACZ;CACD;AAEM,MAAM,WAAqB;IAChC,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAG,WAAU;0BAAqG;;;;;;0BAGnH,8OAAC;gBAAE,WAAU;0BAAgC;;;;;;0BAK7C,8OAAC,gIAAA,CAAA,OAAI;gBAAC,cAAa;;kCACjB,8OAAC,gIAAA,CAAA,WAAQ;;0CACP,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;0CAC9B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAe;;;;;;;;;;;;kCAGpC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC;oCAA6B,SAAS;mCAArB,QAAQ,EAAE;;;;;;;;;;;;;;;kCAKlC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACZ,qBAAqB,GAAG,CAAC,CAAC,wBACzB,8OAAC;oCAA6B,SAAS;mCAArB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1C;AAeA,MAAM,cAA0C,CAAC,EAAE,OAAO,EAAE;IAC1D,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;QAAiB,KAAK;QAAC,QAAQ;;0BAC7C,8OAAC;gBAAI,WAAW,CAAC,uCAAuC,EAAE,QAAQ,QAAQ,CAAC,kEAAkE,CAAC;;;;;;0BAE9I,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,QAAQ,KAAK;;;;;;kEAEhB,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;0DAE1B,8OAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;0DACxB,QAAQ,WAAW;;;;;;;;;;;;kDAIxB,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAS,QAAQ,WAAW,KAAK,UAAU,WAAW;kDAC1D,QAAQ,MAAM;;;;;;;;;;;;0CAKnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,8OAAC;wCAAG,WAAU;kDACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC;gDAAe,WAAU;;kEACxB,8OAAC;wDAAK,WAAU;kEAAqB;;;;;;kEACrC,8OAAC;kEAAM;;;;;;;+CAFA;;;;;;;;;;;;;;;;4BASd,QAAQ,KAAK,CAAC,MAAM,GAAG,mBACtB,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACxB,8OAAC,iIAAA,CAAA,QAAK;wCAEJ,MAAM,KAAK,GAAG;wCACd,QAAO;wCACP,KAAI;wCACJ,SAAQ;wCACR,WAAU;;0DAEV,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAK,WAAU;0DAAW,KAAK,KAAK;;;;;;;uCARhC;;;;;;;;;;;;;;;;kCAef,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,iIAAA,CAAA,aAAU;sCACR,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC/B,8OAAC,iIAAA,CAAA,QAAK;8CAAc;mCAAR;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B", "debugId": null}}, {"offset": {"line": 1450, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/app/page.tsx"], "sourcesContent": ["import { Hero } from '@/components/Hero';\nimport { Experience } from '@/components/Experience';\nimport { Projects } from '@/components/Projects';\n\nexport default function Home() {\n  return (\n    <main className=\"relative min-h-screen w-full flex flex-col\">\n      {/* Background */}\n      <div className=\"fixed inset-0 bg-[var(--background)] w-full\"></div>\n\n      {/* Background Effects */}\n      <div className=\"fixed inset-0\">\n        <div className=\"absolute inset-0 bg-gradient-to-tr from-blue-500/[0.03] via-transparent to-purple-500/[0.03]\"></div>\n        <div className=\"absolute inset-0 bg-[linear-gradient(to_right,var(--card-border)_1px,transparent_1px),linear-gradient(to_bottom,var(--card-border)_1px,transparent_1px)] bg-[size:44px_44px]\"></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative w-full flex-grow\">\n        <div className=\"max-w-3xl sm:w-3/4 mx-auto px-6\">\n          <Hero />\n\n          <div className=\"max-w-2xl mx-auto\">\n            <Experience />\n            <Projects />\n          </div>\n        </div>\n      </div>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAK,WAAU;;0BAEd,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0HAAA,CAAA,OAAI;;;;;sCAEL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,aAAU;;;;;8CACX,8OAAC,8HAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 1577, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAQ/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,CAAA,CAAmB,MAAA,CAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAyB,CAAC,OAAO,CAAI,CAAA,CAAA,CAAA,EAAA,CAClD,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAS9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA;IACvE,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAY,MAAM,CAAA;IAE5B,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,MAAA,CAAO,CAAC,CAAA,CAAE,WAAA,EAAgB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAM,CAAC,CAAA;AAC/D,CAAA;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA,CACR,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA;AAQG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA;IACzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,QAAQ,KAAO,CAAA;QACxB,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,OAAS,CAAA,CAAA;YAC5D,OAAA,CAAA,CAAA,CAAA,CAAA;QAAA;IACT;AAEJ,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1613, "column": 0}, "map": {"version": 3, "file": "defaultAttributes.js", "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,cAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1640, "column": 0}, "map": {"version": 3, "file": "Icon.js", "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACX,CACE,EACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,EACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,EACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,EACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAEL,CAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,+MAAA,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,EACA;QACE,CAAA,CAAA,CAAA;QACA,uKAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,8KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA;QAC3C,CAAI,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAC,cAAA,EAAY,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,MAAO;QAAA,CAAA;QAC/D,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,EACA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA;WACvD,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA;YAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SAAA;KAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1680, "column": 0}, "map": {"version": 3, "file": "createLucideIcon.js", "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,yMACjF,gBAAA,yJAAc,UAAM,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,+KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACT,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,+KAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAA,CAAA,EAC7C,CAAA,OAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,EAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACJ,CAAA;IAGO,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gLAAc,eAAA,EAAa,QAAQ,CAAA;IAEtC,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACT,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1712, "column": 0}, "map": {"version": 3, "file": "github.js", "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/node_modules/lucide-react/src/icons/github.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4',\n      key: 'tonef',\n    },\n  ],\n  ['path', { d: 'M9 18c-4.51 2-5-2-7-2', key: '9comsn' }],\n];\n\n/**\n * @component @name Github\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMjJ2LTRhNC44IDQuOCAwIDAgMC0xLTMuNWMzIDAgNi0yIDYtNS41LjA4LTEuMjUtLjI3LTIuNDgtMS0zLjUuMjgtMS4xNS4yOC0yLjM1IDAtMy41IDAgMC0xIDAtMyAxLjUtMi42NC0uNS01LjM2LS41LTggMEM2IDIgNSAyIDUgMmMtLjMgMS4xNS0uMyAyLjM1IDAgMy41QTUuNDAzIDUuNDAzIDAgMCAwIDQgOWMwIDMuNSAzIDUuNSA2IDUuNS0uMzkuNDktLjY4IDEuMDUtLjg1IDEuNjUtLjE3LjYtLjIyIDEuMjMtLjE1IDEuODV2NCIgLz4KICA8cGF0aCBkPSJNOSAxOGMtNC41MSAyLTUtMi03LTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/github\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n * @deprecated Brand icons have been deprecated and are due to be removed, please refer to https://github.com/lucide-icons/lucide/issues/670. We recommend using https://simpleicons.org/?q=github instead. This icon will be removed in v1.0\n */\nconst Github = createLucideIcon('github', __iconNode);\n\nexport default Github;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACxD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1758, "column": 0}, "map": {"version": 3, "file": "linkedin.js", "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/node_modules/lucide-react/src/icons/linkedin.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z',\n      key: 'c2jq9f',\n    },\n  ],\n  ['rect', { width: '4', height: '12', x: '2', y: '9', key: 'mk3on5' }],\n  ['circle', { cx: '4', cy: '4', r: '2', key: 'bt5ra8' }],\n];\n\n/**\n * @component @name Linkedin\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgOGE2IDYgMCAwIDEgNiA2djdoLTR2LTdhMiAyIDAgMCAwLTItMiAyIDIgMCAwIDAtMiAydjdoLTR2LTdhNiA2IDAgMCAxIDYtNnoiIC8+CiAgPHJlY3Qgd2lkdGg9IjQiIGhlaWdodD0iMTIiIHg9IjIiIHk9IjkiIC8+CiAgPGNpcmNsZSBjeD0iNCIgY3k9IjQiIHI9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/linkedin\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n * @deprecated Brand icons have been deprecated and are due to be removed, please refer to https://github.com/lucide-icons/lucide/issues/670. We recommend using https://simpleicons.org/?q=linkedin instead. This icon will be removed in v1.0\n */\nconst Linkedin = createLucideIcon('linkedin', __iconNode);\n\nexport default Linkedin;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACpE;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CACxD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1816, "column": 0}, "map": {"version": 3, "file": "mail.js", "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/node_modules/lucide-react/src/icons/mail.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7', key: '132q7q' }],\n  ['rect', { x: '2', y: '4', width: '20', height: '16', rx: '2', key: 'izxlao' }],\n];\n\n/**\n * @component @name Mail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjIgNy04Ljk5MSA1LjcyN2EyIDIgMCAwIDEtMi4wMDkgMEwyIDciIC8+CiAgPHJlY3QgeD0iMiIgeT0iNCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjE2IiByeD0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/mail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mail = createLucideIcon('mail', __iconNode);\n\nexport default Mail;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAG,CAAA,CAAA,CAAA,EAAK;YAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1866, "column": 0}, "map": {"version": 3, "file": "phone.js", "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/node_modules/lucide-react/src/icons/phone.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384',\n      key: '9njp5v',\n    },\n  ],\n];\n\n/**\n * @component @name Phone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMuODMyIDE2LjU2OGExIDEgMCAwIDAgMS4yMTMtLjMwM2wuMzU1LS40NjVBMiAyIDAgMCAxIDE3IDE1aDNhMiAyIDAgMCAxIDIgMnYzYTIgMiAwIDAgMS0yIDJBMTggMTggMCAwIDEgMiA0YTIgMiAwIDAgMSAyLTJoM2EyIDIgMCAwIDEgMiAydjNhMiAyIDAgMCAxLS44IDEuNmwtLjQ2OC4zNTFhMSAxIDAgMCAwLS4yOTIgMS4yMzMgMTQgMTQgMCAwIDAgNi4zOTIgNi4zODQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/phone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Phone = createLucideIcon('phone', __iconNode);\n\nexport default Phone;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1905, "column": 0}, "map": {"version": 3, "file": "building-2.js", "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/node_modules/lucide-react/src/icons/building-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z', key: '1b4qmf' }],\n  ['path', { d: 'M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2', key: 'i71pzd' }],\n  ['path', { d: 'M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2', key: '10jefs' }],\n  ['path', { d: 'M10 6h4', key: '1itunk' }],\n  ['path', { d: 'M10 10h4', key: 'tcdvrf' }],\n  ['path', { d: 'M10 14h4', key: 'kelpxr' }],\n  ['path', { d: 'M10 18h4', key: '1ulq68' }],\n];\n\n/**\n * @component @name Building2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiAyMlY0YTIgMiAwIDAgMSAyLTJoOGEyIDIgMCAwIDEgMiAydjE4WiIgLz4KICA8cGF0aCBkPSJNNiAxMkg0YTIgMiAwIDAgMC0yIDJ2NmEyIDIgMCAwIDAgMiAyaDIiIC8+CiAgPHBhdGggZD0iTTE4IDloMmEyIDIgMCAwIDEgMiAydjlhMiAyIDAgMCAxLTIgMmgtMiIgLz4KICA8cGF0aCBkPSJNMTAgNmg0IiAvPgogIDxwYXRoIGQ9Ik0xMCAxMGg0IiAvPgogIDxwYXRoIGQ9Ik0xMCAxNGg0IiAvPgogIDxwYXRoIGQ9Ik0xMCAxOGg0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/building-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Building2 = createLucideIcon('building-2', __iconNode);\n\nexport default Building2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA4C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1986, "column": 0}, "map": {"version": 3, "file": "briefcase.js", "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/node_modules/lucide-react/src/icons/briefcase.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16', key: 'jecpp' }],\n  ['rect', { width: '20', height: '14', x: '2', y: '6', rx: '2', key: 'i6l2r4' }],\n];\n\n/**\n * @component @name Briefcase\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjBWNGEyIDIgMCAwIDAtMi0yaC00YTIgMiAwIDAgMC0yIDJ2MTYiIC8+CiAgPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjE0IiB4PSIyIiB5PSI2IiByeD0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/briefcase\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Briefcase = createLucideIcon('briefcase', __iconNode);\n\nexport default Briefcase;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAS;KAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,EAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK;YAAA,EAAG,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2036, "column": 0}, "map": {"version": 3, "file": "external-link.js", "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/node_modules/lucide-react/src/icons/external-link.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 3h6v6', key: '1q9fwt' }],\n  ['path', { d: 'M10 14 21 3', key: 'gplh6r' }],\n  ['path', { d: 'M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6', key: 'a6xqqp' }],\n];\n\n/**\n * @component @name ExternalLink\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgM2g2djYiIC8+CiAgPHBhdGggZD0iTTEwIDE0IDIxIDMiIC8+CiAgPHBhdGggZD0iTTE4IDEzdjZhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWOGEyIDIgMCAwIDEgMi0yaDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/external-link\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ExternalLink = createLucideIcon('external-link', __iconNode);\n\nexport default ExternalLink;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4D,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3F;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2088, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAA8C,EAAtB,AAAuB;YAAA;gBAEzG,UAAA,CAAA;gBAAA,QAAA;oBAAA,IAAA,0BAA4D;oBAAA;iBAAA;YAC5D;SAAA,KAAO,MAAMC,cAAc,IAAIX,mBAAmB;;KAChDY,YAAY;cACVC,IAAAA,EAAMZ;YAAAA,MAAAA,CAAUa,QAAQ;iBACxBC,MAAM,QAAA;wBAAA;4BACNC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,iBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACV,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAA2C,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BAC3CC,MAAAA,CAAAA,KAAY,OAAA,CAAA;;qBACZC,UAAU;gBACVC,UAAU,EAAE;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}