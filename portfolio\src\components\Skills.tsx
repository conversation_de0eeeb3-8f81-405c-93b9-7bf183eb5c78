import React from 'react';
import { Code, Database, Cloud, Brain, <PERSON>ch, GitBranch } from 'lucide-react';
import { <PERSON>, <PERSON>H<PERSON>er, CardContent, CardTitle, Badge, BadgeGroup } from './ui';

const skillCategories = [
  {
    id: 1,
    title: "Programming Languages",
    icon: Code,
    color: "blue",
    skills: [
      { name: "Python", level: "Primary", description: "Multiple projects, extensive experience" },
      { name: "Java", level: "Intermediate", description: "Academic and project experience" },
      { name: "C++", level: "Basic", description: "Some knowledge, academic background" },
      { name: "JavaScript/TypeScript", level: "Intermediate", description: "Frontend and backend development" }
    ]
  },
  {
    id: 2,
    title: "Frameworks & Technologies",
    icon: Wrench,
    color: "green",
    skills: [
      { name: "Django", level: "Advanced", description: "Backend web development" },
      { name: "React.js", level: "Advanced", description: "Frontend development" },
      { name: "Next.js", level: "Advanced", description: "Full-stack React framework" },
      { name: "Node.js", level: "Intermediate", description: "Backend JavaScript runtime" },
      { name: "Angular", level: "Intermediate", description: "Frontend framework" },
      { name: "Tailwind CSS", level: "Advanced", description: "Utility-first CSS framework" }
    ]
  },
  {
    id: 3,
    title: "Machine Learning & AI",
    icon: Brain,
    color: "purple",
    skills: [
      { name: "TensorFlow", level: "Intermediate", description: "Deep learning framework" },
      { name: "PyTorch", level: "Intermediate", description: "Machine learning library" },
      { name: "Scikit-learn", level: "Advanced", description: "Machine learning algorithms" },
      { name: "Transformers", level: "Intermediate", description: "NLP models" },
      { name: "CNNs & RNNs", level: "Intermediate", description: "Neural network architectures" },
      { name: "OpenAI API", level: "Advanced", description: "GPT-4, GPT-3.5, Codex integration" },
      { name: "Hugging Face", level: "Intermediate", description: "Model hub and transformers" }
    ]
  },
  {
    id: 4,
    title: "Cloud & DevOps",
    icon: Cloud,
    color: "cyan",
    skills: [
      { name: "AWS", level: "Advanced", description: "EC2, S3, Lambda services" },
      { name: "Docker", level: "Intermediate", description: "Containerization" },
      { name: "Kubernetes", level: "Basic", description: "Container orchestration" },
      { name: "CI/CD", level: "Intermediate", description: "Jenkins, GitHub Actions" },
      { name: "Git/GitHub", level: "Advanced", description: "Version control" }
    ]
  },
  {
    id: 5,
    title: "Databases & Tools",
    icon: Database,
    color: "orange",
    skills: [
      { name: "MongoDB", level: "Advanced", description: "NoSQL database" },
      { name: "MySQL", level: "Intermediate", description: "Relational database" },
      { name: "SQLite3", level: "Intermediate", description: "Lightweight database" },
      { name: "Selenium", level: "Advanced", description: "Web scraping and automation" },
      { name: "Tesseract & EasyOCR", level: "Advanced", description: "Image processing and OCR" }
    ]
  },
  {
    id: 6,
    title: "Additional Tools",
    icon: GitBranch,
    color: "pink",
    skills: [
      { name: "RESTful APIs", level: "Advanced", description: "API design and integration" },
      { name: "Socket.io", level: "Intermediate", description: "Real-time communication" },
      { name: "Multi-threading", level: "Advanced", description: "Parallel processing" },
      { name: "Aider CLI", level: "Intermediate", description: "AI-assisted coding" },
      { name: "VS Code", level: "Advanced", description: "Development environment" }
    ]
  }
];

const getColorClass = (color: string) => {
  const colorMap: { [key: string]: string } = {
    blue: 'text-blue-400',
    green: 'text-green-400',
    purple: 'text-purple-400',
    cyan: 'text-cyan-400',
    orange: 'text-orange-400',
    pink: 'text-pink-400'
  };
  return colorMap[color] || 'text-blue-400';
};

export const Skills: React.FC = () => {
  return (
    <section className="py-20">
      <h2 className="text-2xl font-medium mb-3 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text">
        Skills & Technologies
      </h2>
      <p className="text-gray-400 mb-8">
        A comprehensive overview of my technical skills across different domains, 
        from programming languages to cloud technologies and AI/ML frameworks.
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {skillCategories.map((category) => (
          <Card key={category.id} hover className="h-fit">
            <CardHeader>
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 rounded-lg bg-gray-800">
                  <category.icon className={`w-5 h-5 ${getColorClass(category.color)}`} />
                </div>
                <CardTitle className="text-lg">{category.title}</CardTitle>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-4">
                {category.skills.map((skill, index) => (
                  <div key={index} className="flex flex-col gap-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-white">{skill.name}</span>
                      <Badge variant="secondary" className="text-xs">
                        {skill.level}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-400">{skill.description}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
};
