import React from 'react';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'secondary' | 'outline' | 'status';
  className?: string;
  href?: string;
  target?: string;
  rel?: string;
}

export const Badge: React.FC<BadgeProps> = ({ 
  children, 
  variant = 'default', 
  className = '',
  href,
  target,
  rel
}) => {
  const baseClasses = `
    inline-flex items-center justify-center rounded-md border px-2 py-0.5 
    text-xs font-medium w-fit whitespace-nowrap shrink-0 gap-1 
    transition-colors overflow-hidden
  `;

  const variantClasses = {
    default: `
      bg-gray-800 border-gray-700
      hover:bg-gray-700 hover:border-gray-600
      text-white
    `,
    secondary: `
      bg-blue-500/10 text-blue-400 hover:bg-blue-500/20
      border-transparent
    `,
    outline: `
      border-gray-700 bg-transparent
      hover:bg-gray-800 text-gray-300
    `,
    status: `
      bg-green-500/10 text-green-400 hover:bg-green-500/20
      border-green-500/20
    `
  };

  const classes = `${baseClasses} ${variantClasses[variant]} ${className}`;

  if (href) {
    return (
      <a 
        href={href} 
        target={target} 
        rel={rel}
        className={`${classes} hover:no-underline`}
      >
        {children}
      </a>
    );
  }

  return (
    <span className={classes}>
      {children}
    </span>
  );
};

interface BadgeGroupProps {
  children: React.ReactNode;
  className?: string;
}

export const BadgeGroup: React.FC<BadgeGroupProps> = ({ children, className = '' }) => {
  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {children}
    </div>
  );
};
