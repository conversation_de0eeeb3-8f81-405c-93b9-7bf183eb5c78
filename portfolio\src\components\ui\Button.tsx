import React from 'react';

interface ButtonProps {
  children: React.ReactNode;
  variant?: 'default' | 'ghost' | 'outline' | 'link';
  size?: 'sm' | 'md' | 'lg' | 'icon';
  className?: string;
  onClick?: () => void;
  href?: string;
  target?: string;
  rel?: string;
  disabled?: boolean;
}

export const Button: React.FC<ButtonProps> = ({ 
  children, 
  variant = 'default', 
  size = 'md',
  className = '',
  onClick,
  href,
  target,
  rel,
  disabled = false
}) => {
  const baseClasses = `
    inline-flex items-center justify-center rounded-lg font-medium 
    transition-colors focus-visible:outline-none focus-visible:ring-2 
    focus-visible:ring-ring focus-visible:ring-offset-2 
    disabled:pointer-events-none disabled:opacity-50
  `;

  const variantClasses = {
    default: `
      bg-[var(--text-primary)] text-[var(--background)] 
      hover:bg-[var(--text-secondary)]
    `,
    ghost: `
      hover:bg-[var(--hover-background)] hover:text-[var(--text-primary)]
      text-[var(--text-secondary)]
    `,
    outline: `
      border border-[var(--card-border)] bg-transparent 
      hover:bg-[var(--hover-background)] hover:text-[var(--text-primary)]
      text-[var(--text-secondary)]
    `,
    link: `
      text-[var(--text-primary)] underline-offset-4 hover:underline
      bg-transparent
    `
  };

  const sizeClasses = {
    sm: 'h-8 px-3 text-xs',
    md: 'h-10 px-4 py-2',
    lg: 'h-11 px-8',
    icon: 'h-10 w-10'
  };

  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;

  if (href) {
    return (
      <a 
        href={href} 
        target={target} 
        rel={rel}
        className={`${classes} no-underline`}
      >
        {children}
      </a>
    );
  }

  return (
    <button 
      className={classes}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
};
