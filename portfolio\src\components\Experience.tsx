import React from 'react';
import { Building2, Briefcase } from 'lucide-react';
import { <PERSON>, Card<PERSON>eader, CardContent, CardTitle, CardDescription, Badge, BadgeGroup, Timeline, TimelineItem } from './ui';

const experiences = [
  {
    id: 1,
    title: "Lead Developer (Full Stack & Cloud Infrastructure)",
    company: "Undisclosed Client (Electoral Platform)",
    duration: "Nov 2024 - Feb 2025",
    location: "Mumbai, India",
    status: "Current",
    description: "Pioneered India's first fully streamlined electoral-sampling platform",
    achievements: [
      "Led end-to-end R&D and full-stack implementation to eliminate manual data collection",
      "Built distributed web scraping system on AWS EC2 with S3 for data storage",
      "Reduced data collection time by 9600 man-days worth of work",
      "Engineered high-throughput data extraction achieving 50x improvement (4000 per hour)",
      "Built advanced image processing modules with 97% accuracy in data extraction"
    ],
    technologies: ["Python", "AWS EC2", "AWS S3", "Selenium", "Tesseract", "EasyOCR", "Multi-threading"],
    links: [
      { label: "Platform", url: "https://edownloaders.com", icon: "globe" }
    ],
    icon: Building2,
    color: "blue"
  },
  {
    id: 2,
    title: "Software Intern (Business Development Automation)",
    company: "Viven Ediversity Pvt. Ltd.",
    duration: "June 2024 - Jan 2025",
    location: "Thane, India",
    status: "Past",
    description: "Executed comprehensive website testing and business automation solutions",
    achievements: [
      "Launched 60 courses with 100% positive response rate",
      "Integrated WhatsApp Business API saving 3+ hours daily",
      "Automated 500+ warm emails daily through custom solutions",
      "Implemented SEO best practices and endpoint integrations"
    ],
    technologies: ["Google Sheets Apps Script", "Pabbly", "WhatsApp Business API", "SEO"],
    links: [],
    icon: Briefcase,
    color: "green"
  }
];

export const Experience: React.FC = () => {
  return (
    <section className="py-20">
      <h2 className="text-2xl font-medium mb-3 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text">
        Experience
      </h2>
      <p className="text-[var(--text-muted)] mb-8">
        Here's a timeline of my professional journey, showcasing my roles and contributions in 
        full-stack development, cloud infrastructure, and business automation.
      </p>
      
      <Timeline>
        {experiences.map((exp) => (
          <TimelineItem key={exp.id}>
            <Card gradient hover>
              <CardHeader>
                <div className="flex flex-col sm:flex-row items-start gap-4">
                  <div className="p-2 rounded-lg bg-[var(--hover-background)]">
                    <exp.icon className={`w-5 h-5 text-${exp.color}-400`} />
                  </div>
                  
                  <div className="space-y-1.5 flex-grow w-full">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                      <div className="flex flex-wrap items-center gap-2">
                        <CardTitle>{exp.title}</CardTitle>
                        <span className="text-sm text-[var(--text-muted)]">•</span>
                        <span className="text-[var(--text-muted)]">{exp.company}</span>
                      </div>
                      
                      <div className="flex flex-wrap gap-2">
                        {exp.links.map((link, index) => (
                          <Badge 
                            key={index}
                            href={link.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            variant="outline"
                          >
                            {link.label}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div className="text-sm text-[var(--text-muted)]">{exp.duration}</div>
                    <div className="text-sm text-[var(--text-muted)]">{exp.location}</div>
                    <CardDescription>{exp.description}</CardDescription>
                    
                    {/* Achievements */}
                    <div className="mt-3">
                      <h4 className="text-sm font-medium text-[var(--text-primary)] mb-2">Key Achievements:</h4>
                      <ul className="text-sm text-[var(--text-muted)] space-y-1">
                        {exp.achievements.map((achievement, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="text-blue-400 mt-1">•</span>
                            <span>{achievement}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <BadgeGroup>
                  {exp.technologies.map((tech, index) => (
                    <Badge key={index}>{tech}</Badge>
                  ))}
                </BadgeGroup>
              </CardContent>
            </Card>
          </TimelineItem>
        ))}
      </Timeline>
    </section>
  );
};
