<!DOCTYPE html>
<!-- saved from url=(0033)https://www.chaitanya-bajpai.xyz/ -->
<html lang="en" class="__variable_3a0388 __variable_c1e5c9" data-theme="dark" style="color-scheme: dark;"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1"><link rel="preload" href="https://www.chaitanya-bajpai.xyz/_next/static/media/66f30814ff6d7cdf.p.woff2" as="font" crossorigin="" type="font/woff2"><link rel="preload" href="https://www.chaitanya-bajpai.xyz/_next/static/media/e11418ac562b8ac1-s.p.woff2" as="font" crossorigin="" type="font/woff2"><link rel="preload" as="image" imagesrcset="/_next/image?url=%2Fpfp.jpeg&amp;w=640&amp;q=75 640w, /_next/image?url=%2Fpfp.jpeg&amp;w=750&amp;q=75 750w, /_next/image?url=%2Fpfp.jpeg&amp;w=828&amp;q=75 828w, /_next/image?url=%2Fpfp.jpeg&amp;w=1080&amp;q=75 1080w, /_next/image?url=%2Fpfp.jpeg&amp;w=1200&amp;q=75 1200w, /_next/image?url=%2Fpfp.jpeg&amp;w=1920&amp;q=75 1920w, /_next/image?url=%2Fpfp.jpeg&amp;w=2048&amp;q=75 2048w, /_next/image?url=%2Fpfp.jpeg&amp;w=3840&amp;q=75 3840w" imagesizes="100vw"><link rel="stylesheet" href="./Chaitanya Bajpai_files/ee9329db790c09cb.css" data-precedence="next"><link rel="preload" as="script" fetchpriority="low" href="./Chaitanya Bajpai_files/webpack-c69c9ac8f19d6745.js.download"><script src="./Chaitanya Bajpai_files/96e220d1-97bb9cdb23d5bfb5.js.download" async=""></script><script src="./Chaitanya Bajpai_files/770-7169cc5800fc9060.js.download" async=""></script><script src="./Chaitanya Bajpai_files/main-app-1fc617c28b4b492d.js.download" async=""></script><meta name="next-size-adjust" content=""><title>Chaitanya Bajpai</title><meta name="description" content="Full Stack Blockchain Developer"><link rel="icon" href="https://www.chaitanya-bajpai.xyz/favicon.ico" type="image/x-icon" sizes="1920x1080"><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><style type="text/css" id="operaUserStyle"></style><script src="./Chaitanya Bajpai_files/polyfills-42372ed130431b0a.js.download" nomodule=""></script><script src="./Chaitanya Bajpai_files/script.js.download" defer="" data-sdkn="@vercel/analytics/react" data-sdkv="1.5.0"></script></head><body class="__className_3a0388 vsc-initialized"><script>((e,t,r,n,o,a,i,u)=>{let l=document.documentElement,s=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&a?o.map(e=>a[e]||e):o;r?(l.classList.remove(...n),l.classList.add(a&&a[t]?a[t]:t)):l.setAttribute(e,t)}),r=t,u&&s.includes(r)&&(l.style.colorScheme=r)}if(n)c(n);else try{let e=localStorage.getItem(t)||r,n=i&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(n)}catch(e){}})("data-theme","theme","dark",null,["light","dark"],null,true,true)</script><main class="relative min-h-screen w-full flex flex-col"><div class="fixed inset-0 bg-[var(--background)] w-full"></div><div class="fixed inset-0"><div class="absolute inset-0 bg-gradient-to-tr from-blue-500/[0.03] via-transparent to-purple-500/[0.03]"></div><div class="absolute inset-0 bg-[linear-gradient(to_right,var(--card-border)_1px,transparent_1px),linear-gradient(to_bottom,var(--card-border)_1px,transparent_1px)] bg-[size:44px_44px]"></div></div><div class="relative w-full flex-grow"><div class="max-w-3xl sm:w-3/4 mx-auto px-6"><section><div class="flex justify-between items-start mb-8 pt-20" id="ca426f02-0d88-47a3-a431-3965a58b1d3b"><div class="relative w-[128px] h-[128px] rounded-2xl overflow-hidden"><img alt="Profile picture" decoding="async" data-nimg="fill" class="h-28 w-28 rounded-3xl object-cover" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100vw" srcset="/_next/image?url=%2Fpfp.jpeg&amp;w=640&amp;q=75 640w, /_next/image?url=%2Fpfp.jpeg&amp;w=750&amp;q=75 750w, /_next/image?url=%2Fpfp.jpeg&amp;w=828&amp;q=75 828w, /_next/image?url=%2Fpfp.jpeg&amp;w=1080&amp;q=75 1080w, /_next/image?url=%2Fpfp.jpeg&amp;w=1200&amp;q=75 1200w, /_next/image?url=%2Fpfp.jpeg&amp;w=1920&amp;q=75 1920w, /_next/image?url=%2Fpfp.jpeg&amp;w=2048&amp;q=75 2048w, /_next/image?url=%2Fpfp.jpeg&amp;w=3840&amp;q=75 3840w" src="./Chaitanya Bajpai_files/pfp.jpg"></div><div class="flex items-center gap-6"><a target="_blank" class="w-10 h-10 flex items-center justify-center hover:bg-[var(--hover-background)] rounded-lg transition-colors" data-state="closed" data-slot="tooltip-trigger" href="https://x.com/cbajpai7"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-twitter"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg></a><a target="_blank" class="w-10 h-10 flex items-center justify-center hover:bg-[var(--hover-background)] rounded-lg transition-colors" data-state="closed" data-slot="tooltip-trigger" href="https://medium.com/@cb7chaitanya"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-open"><path d="M12 7v14"></path><path d="M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z"></path></svg></a><a target="_blank" class="w-10 h-10 flex items-center justify-center hover:bg-[var(--hover-background)] rounded-lg transition-colors" data-state="closed" data-slot="tooltip-trigger" href="https://github.com/cb7chaitanya"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-github"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg></a></div></div><h1 class="text-[32px] leading-none font-medium text-[var(--text-primary)] mb-2">Hi, I'm Chaitanya</h1><p class="text-[var(--text-secondary)] text-base mb-4">21, Delhi | Full Stack Engineer</p><p class="text-[var(--text-secondary)] max-w-xl">I'm a Full Stack Blockchain Developer crafting cutting-edge dApps and DeFi solutions. From writing secure smart contracts to building intuitive Web3 interfaces, I turn complex blockchain concepts into user-friendly experiences.</p></section><div class="max-w-2xl mx-auto"><div class="relative mx-auto w-full max-w-4xl h-fit"><div class="absolute top-3 -left-4 md:-left-20 hidden md:block"><div class="border-netural-200 ml-[27px] flex h-4 w-4 items-center justify-center rounded-full border shadow-sm" style="box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;"><div class="h-2 w-2 rounded-full border border-neutral-300 bg-white" style="background-color: rgb(16, 185, 129); border-color: rgb(5, 150, 105);"></div></div><svg viewBox="0 0 20 3140" width="20" height="3140" class="ml-4 block" aria-hidden="true"><path d="M 1 0V -36 l 18 24 V 2512 l -18 24V 3140" fill="none" stroke="#9091A0" stroke-opacity="0.16"></path><path d="M 1 0V -36 l 18 24 V 2512 l -18 24V 3140" fill="none" stroke="url(#gradient)" stroke-width="1.25" class="motion-reduce:hidden"></path><defs><lineargradient id="gradient" gradientUnits="userSpaceOnUse" x1="0" x2="0" y1="2844.2869344506103" y2="2140.7415509546313"><stop stop-color="#18CCFC" stop-opacity="0"></stop><stop stop-color="#18CCFC"></stop><stop offset="0.325" stop-color="#6344F5"></stop><stop offset="1" stop-color="#AE48FF" stop-opacity="0"></stop></lineargradient></defs></svg></div><div><section class="py-20"><h2 class="text-2xl font-medium mb-3 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text">Experience</h2><p class="text-[var(--text-muted)] mb-8">Here's a timeline of my professional journey, showcasing my roles and contributions in blockchain and full-stack development.</p><div class="space-y-6"><div style="opacity: 1; transform: none;"><div data-slot="card" class="rounded-lg border flex flex-col gap-6 shadow-sm py-6 text-[var(--text-primary)] group relative border-[var(--card-border)] bg-[var(--card-background)] hover:bg-[var(--hover-background)] transition-all duration-300 overflow-hidden cursor-pointer"><div class="absolute left-0 top-0 h-full w-1 bg-gradient-to-b from-blue-500/20 via-cyan-500/20 to-transparent"></div><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-4"><div class="flex flex-col sm:flex-row items-start gap-4"><div class="p-2 rounded-lg bg-[var(--hover-background)]"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-building2 lucide-building-2 w-5 h-5 text-blue-400"><path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"></path><path d="M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"></path><path d="M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"></path><path d="M10 6h4"></path><path d="M10 10h4"></path><path d="M10 14h4"></path><path d="M10 18h4"></path></svg></div><div class="space-y-1.5 flex-grow w-full"><div class="flex flex-col sm:flex-row sm:items-center justify-between gap-2"><div class="flex flex-wrap items-center gap-2"><div data-slot="card-title" class="font-semibold text-lg text-[var(--text-primary)]">Full-Stack Blockchain Engineer</div><span class="text-sm text-[var(--text-muted)]">•</span><span class="text-[var(--text-muted)]">Wildcard</span></div><div class="flex flex-wrap gap-2"></div></div><div class="text-sm text-[var(--text-muted)]">Current</div><div data-slot="card-description" class="text-sm text-[var(--text-muted)]">Building smart wallet infrastructure and blockchain applications</div></div></div></div><div data-slot="card-content" class="px-6 pt-0 pb-4"><div class="flex flex-wrap gap-2"><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Rust</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Solana</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">EVM</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Next.js</span></div></div></div></div><div style="opacity: 1; transform: none;"><div data-slot="card" class="rounded-lg border flex flex-col gap-6 shadow-sm py-6 text-[var(--text-primary)] group relative border-[var(--card-border)] bg-[var(--card-background)] hover:bg-[var(--hover-background)] transition-all duration-300 overflow-hidden cursor-pointer"><div class="absolute left-0 top-0 h-full w-1 bg-gradient-to-b from-blue-500/20 via-cyan-500/20 to-transparent"></div><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-4"><div class="flex flex-col sm:flex-row items-start gap-4"><div class="p-2 rounded-lg bg-[var(--hover-background)]"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-blocks w-5 h-5 text-green-400"><rect width="7" height="7" x="14" y="3" rx="1"></rect><path d="M10 21V8a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-5a1 1 0 0 0-1-1H3"></path></svg></div><div class="space-y-1.5 flex-grow w-full"><div class="flex flex-col sm:flex-row sm:items-center justify-between gap-2"><div class="flex flex-wrap items-center gap-2"><div data-slot="card-title" class="font-semibold text-lg text-[var(--text-primary)]">Full-Stack Engineer</div><span class="text-sm text-[var(--text-muted)]">•</span><span class="text-[var(--text-muted)]">Swifey AI</span></div><div class="flex flex-wrap gap-2"><a href="https://github.com/cb7chaitanya/swifey-contracts" target="_blank" rel="noopener noreferrer" class="group"><span data-slot="badge" class="justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors flex items-center gap-1.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code-xml w-3 h-3"><path d="m18 16 4-4-4-4"></path><path d="m6 8-4 4 4 4"></path><path d="m14.5 4-5 16"></path></svg><span class="text-xs">Contracts</span></span></a><a href="https://apps.apple.com/us/app/swifey-dating/id6737560814" target="_blank" rel="noopener noreferrer" class="group"><span data-slot="badge" class="justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors flex items-center gap-1.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe w-3 h-3"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg><span class="text-xs">App Store</span></span></a><a href="https://x.com/SwifeyAI" target="_blank" rel="noopener noreferrer" class="group"><span data-slot="badge" class="justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors flex items-center gap-1.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-twitter w-3 h-3"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg><span class="text-xs">Twitter</span></span></a></div></div><div class="text-sm text-[var(--text-muted)]">Past</div><div data-slot="card-description" class="text-sm text-[var(--text-muted)]">Full-stack development across web, mobile, and blockchain</div></div></div></div><div data-slot="card-content" class="px-6 pt-0 pb-4"><div class="flex flex-wrap gap-2"><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">FastAPI</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Node.js</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">React</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">TypeScript</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Flutter</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Solana</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Rust</span></div></div></div></div><div style="opacity: 1; transform: none;"><div data-slot="card" class="rounded-lg border flex flex-col gap-6 shadow-sm py-6 text-[var(--text-primary)] group relative border-[var(--card-border)] bg-[var(--card-background)] hover:bg-[var(--hover-background)] transition-all duration-300 overflow-hidden cursor-pointer"><div class="absolute left-0 top-0 h-full w-1 bg-gradient-to-b from-blue-500/20 via-cyan-500/20 to-transparent"></div><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-4"><div class="flex flex-col sm:flex-row items-start gap-4"><div class="p-2 rounded-lg bg-[var(--hover-background)]"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code-xml w-5 h-5 text-purple-400"><path d="m18 16 4-4-4-4"></path><path d="m6 8-4 4 4 4"></path><path d="m14.5 4-5 16"></path></svg></div><div class="space-y-1.5 flex-grow w-full"><div class="flex flex-col sm:flex-row sm:items-center justify-between gap-2"><div class="flex flex-wrap items-center gap-2"><div data-slot="card-title" class="font-semibold text-lg text-[var(--text-primary)]">Founding Engineer</div><span class="text-sm text-[var(--text-muted)]">•</span><span class="text-[var(--text-muted)]">Veritas AO</span></div><div class="flex flex-wrap gap-2"><a href="https://veritas-ao.dev/" target="_blank" rel="noopener noreferrer" class="group"><span data-slot="badge" class="justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors flex items-center gap-1.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe w-3 h-3"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg><span class="text-xs">Website</span></span></a><a href="https://x.com/Veritas_ao" target="_blank" rel="noopener noreferrer" class="group"><span data-slot="badge" class="justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors flex items-center gap-1.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-twitter w-3 h-3"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg><span class="text-xs">Twitter</span></span></a></div></div><div class="text-sm text-[var(--text-muted)]">2024</div><div data-slot="card-description" class="text-sm text-[var(--text-muted)]">Building fair launch platform on Arweave's AO protocol</div></div></div></div><div data-slot="card-content" class="px-6 pt-0 pb-4"><div class="flex flex-wrap gap-2"><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">AO</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">TypeScript</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">React</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Node.js</span></div></div></div></div><div style="opacity: 1; transform: none;"><div data-slot="card" class="rounded-lg border flex flex-col gap-6 shadow-sm py-6 text-[var(--text-primary)] group relative border-[var(--card-border)] bg-[var(--card-background)] hover:bg-[var(--hover-background)] transition-all duration-300 overflow-hidden cursor-pointer"><div class="absolute left-0 top-0 h-full w-1 bg-gradient-to-b from-blue-500/20 via-cyan-500/20 to-transparent"></div><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-4"><div class="flex flex-col sm:flex-row items-start gap-4"><div class="p-2 rounded-lg bg-[var(--hover-background)]"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-cpu w-5 h-5 text-pink-400"><rect width="16" height="16" x="4" y="4" rx="2"></rect><rect width="6" height="6" x="9" y="9" rx="1"></rect><path d="M15 2v2"></path><path d="M15 20v2"></path><path d="M2 15h2"></path><path d="M2 9h2"></path><path d="M20 15h2"></path><path d="M20 9h2"></path><path d="M9 2v2"></path><path d="M9 20v2"></path></svg></div><div class="space-y-1.5 flex-grow w-full"><div class="flex flex-col sm:flex-row sm:items-center justify-between gap-2"><div class="flex flex-wrap items-center gap-2"><div data-slot="card-title" class="font-semibold text-lg text-[var(--text-primary)]">Full-Stack Engineering Intern</div><span class="text-sm text-[var(--text-muted)]">•</span><span class="text-[var(--text-muted)]">Grafieks</span></div><div class="flex flex-wrap gap-2"><a href="https://grafieks.com/" target="_blank" rel="noopener noreferrer" class="group"><span data-slot="badge" class="justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors flex items-center gap-1.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe w-3 h-3"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg><span class="text-xs">Website</span></span></a><a href="https://www.linkedin.com/company/grafieks" target="_blank" rel="noopener noreferrer" class="group"><span data-slot="badge" class="justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors flex items-center gap-1.5"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-linkedin w-3 h-3"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect width="4" height="12" x="2" y="9"></rect><circle cx="4" cy="4" r="2"></circle></svg><span class="text-xs">LinkedIn</span></span></a></div></div><div class="text-sm text-[var(--text-muted)]">Internship</div><div data-slot="card-description" class="text-sm text-[var(--text-muted)]">Full-stack development internship</div></div></div></div><div data-slot="card-content" class="px-6 pt-0 pb-4"><div class="flex flex-wrap gap-2"><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Go</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Node.js</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">React</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">TypeScript</span></div></div></div></div></div></section><section class="py-20" style="opacity: 1; transform: none;"><h2 class="text-2xl font-medium mb-3 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text">Projects</h2><p class="text-[var(--text-muted)] mb-8">A collection of my work spanning from blockchain applications to full-stack projects, both personal and professional.</p><div dir="ltr" data-orientation="horizontal" data-slot="tabs" class="flex flex-col gap-2 w-full"><div role="tablist" aria-orientation="horizontal" data-slot="tabs-list" class="text-muted-foreground h-9 items-center justify-center rounded-lg p-[3px] grid w-full grid-cols-2 mb-8 bg-[var(--card-background)] border-[var(--card-border)]" tabindex="0" data-orientation="horizontal" style="outline:none"><button type="button" role="tab" aria-selected="true" aria-controls="radix-«Rtaj7db»-content-personal" data-state="active" id="radix-«Rtaj7db»-trigger-personal" data-slot="tabs-trigger" class="dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg:not([class*=&#39;size-&#39;])]:size-4 data-[state=active]:bg-[var(--hover-background)] data-[state=active]:text-[var(--text-primary)]" tabindex="0" data-orientation="horizontal" data-radix-collection-item="">Personal Projects</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«Rtaj7db»-content-client" data-state="inactive" id="radix-«Rtaj7db»-trigger-client" data-slot="tabs-trigger" class="dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg:not([class*=&#39;size-&#39;])]:size-4 data-[state=active]:bg-[var(--hover-background)] data-[state=active]:text-[var(--text-primary)]" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Client Work</button></div><div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«Rtaj7db»-trigger-personal" id="radix-«Rtaj7db»-content-personal" tabindex="0" data-slot="tabs-content" class="flex-1 outline-none mt-0" style=""><div class="grid grid-cols-1 md:grid-cols-2 gap-6 min-h-[400px]"><div style="opacity: 1; transform: none;"><div data-slot="card" class="rounded-lg border flex flex-col gap-6 shadow-sm py-6 text-[var(--text-primary)] group relative border-[var(--card-border)] bg-[var(--card-background)] hover:bg-[var(--hover-background)] transition-all duration-300 overflow-hidden"><div class="absolute -inset-[1px] bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-red-600/20 opacity-0 group-hover:opacity-100 transition duration-500 blur-sm"></div><a href="https://github.com/cb7chaitanya/streamly" class="relative block" target="_blank" rel="noopener noreferrer"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-6"><div class="flex items-start justify-between"><div class="space-y-3"><div data-slot="card-title" class="font-semibold flex items-center gap-2 text-lg group-hover:text-[var(--text-primary)] transition-colors">Streamly<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:translate-x-1"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div><div data-slot="card-description" class="text-muted-foreground text-sm line-clamp-2">Streamyard alternative with custom SFU server and FFMPEG stream mixing</div></div><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent [a&amp;]:hover:bg-secondary/90 bg-blue-500/10 text-blue-400 hover:bg-blue-500/20">Paused</span></div></div><div data-slot="card-content" class="px-6"><div class="flex flex-wrap gap-3"><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">WebRTC</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Mediasoup</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">FFMPEG</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Node.js</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">React</span></div></div></a></div></div><div style="opacity: 1; transform: none;"><div data-slot="card" class="rounded-lg border flex flex-col gap-6 shadow-sm py-6 text-[var(--text-primary)] group relative border-[var(--card-border)] bg-[var(--card-background)] hover:bg-[var(--hover-background)] transition-all duration-300 overflow-hidden"><div class="absolute -inset-[1px] bg-gradient-to-r from-cyan-600/20 via-blue-600/20 to-indigo-600/20 opacity-0 group-hover:opacity-100 transition duration-500 blur-sm"></div><a href="https://github.com/SnipSavvy/SnipSavvy_Frontend" class="relative block" target="_blank" rel="noopener noreferrer"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-6"><div class="flex items-start justify-between"><div class="space-y-3"><div data-slot="card-title" class="font-semibold flex items-center gap-2 text-lg group-hover:text-[var(--text-primary)] transition-colors">SnipSavvy<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:translate-x-1"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div><div data-slot="card-description" class="text-muted-foreground text-sm line-clamp-2">Code snippet management platform with multi-level sharing and organization capabilities</div></div></div></div><div data-slot="card-content" class="px-6"><div class="flex flex-wrap gap-3"><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Next.js</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">TypeScript</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Tailwind</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Node.js</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">MongoDB</span></div></div></a></div></div><div style="opacity: 1; transform: none;"><div data-slot="card" class="rounded-lg border flex flex-col gap-6 shadow-sm py-6 text-[var(--text-primary)] group relative border-[var(--card-border)] bg-[var(--card-background)] hover:bg-[var(--hover-background)] transition-all duration-300 overflow-hidden"><div class="absolute -inset-[1px] bg-gradient-to-r from-indigo-600/20 via-blue-600/20 to-cyan-600/20 opacity-0 group-hover:opacity-100 transition duration-500 blur-sm"></div><a href="https://github.com/cb7chaitanya/StreamVault" class="relative block" target="_blank" rel="noopener noreferrer"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-6"><div class="flex items-start justify-between"><div class="space-y-3"><div data-slot="card-title" class="font-semibold flex items-center gap-2 text-lg group-hover:text-[var(--text-primary)] transition-colors">Stream-Vault<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:translate-x-1"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div><div data-slot="card-description" class="text-muted-foreground text-sm line-clamp-2">Decentralized video platform on WeaveVM with on-chain storage and EVM integration</div></div></div></div><div data-slot="card-content" class="px-6"><div class="flex flex-wrap gap-3"><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Arweave</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">EVM</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">React</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Node.js</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Solidity</span></div></div></a></div></div><div style="opacity: 1; transform: none;"><div data-slot="card" class="rounded-lg border flex flex-col gap-6 shadow-sm py-6 text-[var(--text-primary)] group relative border-[var(--card-border)] bg-[var(--card-background)] hover:bg-[var(--hover-background)] transition-all duration-300 overflow-hidden"><div class="absolute -inset-[1px] bg-gradient-to-r from-emerald-600/20 via-teal-600/20 to-cyan-600/20 opacity-0 group-hover:opacity-100 transition duration-500 blur-sm"></div><a href="https://github.com/cb7chaitanya/exchange" class="relative block" target="_blank" rel="noopener noreferrer"><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-6"><div class="flex items-start justify-between"><div class="space-y-3"><div data-slot="card-title" class="font-semibold flex items-center gap-2 text-lg group-hover:text-[var(--text-primary)] transition-colors">Rust CEX Backend<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:translate-x-1"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div><div data-slot="card-description" class="text-muted-foreground text-sm line-clamp-2">High-performance crypto exchange backend with trade matching and pub/sub system</div></div></div></div><div data-slot="card-content" class="px-6"><div class="flex flex-wrap gap-3"><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Rust</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Actix Web</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">PostgreSQL</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">Redis</span><span data-slot="badge" class="inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&amp;&gt;svg]:size-3 gap-1 [&amp;&gt;svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive overflow-hidden text-foreground [a&amp;]:hover:bg-accent [a&amp;]:hover:text-accent-foreground bg-[var(--hover-background)] border-[var(--card-border)] hover:bg-[var(--card-background)] hover:border-[var(--text-muted)] transition-colors">TimescaleDB</span></div></div></a></div></div></div></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«Rtaj7db»-trigger-client" id="radix-«Rtaj7db»-content-client" tabindex="0" data-slot="tabs-content" class="flex-1 outline-none mt-0" hidden=""></div></div></section><section class="py-20" style="opacity: 1; transform: none;"><h2 class="text-2xl font-medium mb-3 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text">Blogs</h2><p class="text-[var(--text-muted)] mb-8">I write about software development, sharing insights and experiences from my journey in tech.</p><div class="space-y-4"><div style="opacity: 1; transform: none;"><a href="https://medium.com/@cb7chaitanya/when-process-env-bites-back-a-node-js-performance-lesson-40bbec066d33" target="_blank" rel="noopener noreferrer"><div data-slot="card" class="rounded-lg border flex flex-col gap-6 shadow-sm py-6 text-[var(--text-primary)] group relative border-[var(--card-border)] bg-[var(--card-background)] hover:bg-[var(--hover-background)] transition-all duration-300 overflow-hidden"><div class="absolute -inset-[1px] bg-gradient-to-r from-blue-500/20 via-cyan-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition duration-500 blur-sm"></div><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 relative"><div class="space-y-2"><div class="flex items-start justify-between"><div data-slot="card-title" class="font-semibold text-lg group-hover:text-[var(--text-primary)] transition-colors flex items-center gap-2">When process.env Bites Back: A Node.js Performance Lesson<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:translate-x-1"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div></div><div data-slot="card-description" class="text-muted-foreground text-sm line-clamp-2">During a past job, I was working on optimizing an internal API service. Here's what we had:</div><div class="flex items-center gap-3 text-sm text-[var(--text-muted)]"><span>May 4, 2025</span></div></div></div></div></a></div><div style="opacity: 1; transform: none;"><a href="https://medium.com/stackademic/cookie-based-authentication-a-simple-guide-for-secure-sessions-d8f0549e8f86" target="_blank" rel="noopener noreferrer"><div data-slot="card" class="rounded-lg border flex flex-col gap-6 shadow-sm py-6 text-[var(--text-primary)] group relative border-[var(--card-border)] bg-[var(--card-background)] hover:bg-[var(--hover-background)] transition-all duration-300 overflow-hidden"><div class="absolute -inset-[1px] bg-gradient-to-r from-blue-500/20 via-cyan-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition duration-500 blur-sm"></div><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 relative"><div class="space-y-2"><div class="flex items-start justify-between"><div data-slot="card-title" class="font-semibold text-lg group-hover:text-[var(--text-primary)] transition-colors flex items-center gap-2">Cookie-based Authentication: A Simple Guide for Secure Sessions<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:translate-x-1"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div></div><div data-slot="card-description" class="text-muted-foreground text-sm line-clamp-2">What is Authentication? Authentication is the process of verifying the identity of a user, device, or entity in a system. It ensures that...</div><div class="flex items-center gap-3 text-sm text-[var(--text-muted)]"><span>Jul 12, 2024</span></div></div></div></div></a></div><div style="opacity: 1; transform: none;"><a href="https://medium.com/stackademic/understanding-typescripts-handling-of-object-literal-types-the-quirks-and-insights-c1c8b4e49645" target="_blank" rel="noopener noreferrer"><div data-slot="card" class="rounded-lg border flex flex-col gap-6 shadow-sm py-6 text-[var(--text-primary)] group relative border-[var(--card-border)] bg-[var(--card-background)] hover:bg-[var(--hover-background)] transition-all duration-300 overflow-hidden"><div class="absolute -inset-[1px] bg-gradient-to-r from-blue-500/20 via-cyan-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition duration-500 blur-sm"></div><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 relative"><div class="space-y-2"><div class="flex items-start justify-between"><div data-slot="card-title" class="font-semibold text-lg group-hover:text-[var(--text-primary)] transition-colors flex items-center gap-2">Understanding TypeScript's Handling of Object Literal Types<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:translate-x-1"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div></div><div data-slot="card-description" class="text-muted-foreground text-sm line-clamp-2">Did you know that TypeScript's handling of object literals can sometimes lead to unexpected behavior? when I started learning...</div><div class="flex items-center gap-3 text-sm text-[var(--text-muted)]"><span>Jun 20, 2024</span></div></div></div></div></a></div><div style="opacity: 1; transform: none;"><a href="https://medium.com/stackademic/why-choose-nextjs-over-react-74ac9eeca76d" target="_blank" rel="noopener noreferrer"><div data-slot="card" class="rounded-lg border flex flex-col gap-6 shadow-sm py-6 text-[var(--text-primary)] group relative border-[var(--card-border)] bg-[var(--card-background)] hover:bg-[var(--hover-background)] transition-all duration-300 overflow-hidden"><div class="absolute -inset-[1px] bg-gradient-to-r from-blue-500/20 via-cyan-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition duration-500 blur-sm"></div><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 relative"><div class="space-y-2"><div class="flex items-start justify-between"><div data-slot="card-title" class="font-semibold text-lg group-hover:text-[var(--text-primary)] transition-colors flex items-center gap-2">Why Choose Next.js over React?<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:translate-x-1"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div></div><div data-slot="card-description" class="text-muted-foreground text-sm line-clamp-2">In the dynamic landscape of web development, React has emerged as a powerhouse for building interactive user interfaces. However, as projects grow in complexity...</div><div class="flex items-center gap-3 text-sm text-[var(--text-muted)]"><span>Apr 14, 2024</span></div></div></div></div></a></div><div style="opacity: 1; transform: none;"><a href="https://medium.com/@cb7chaitanya/need-of-context-api-or-similar-state-management-tools-12c3d3b9f500" target="_blank" rel="noopener noreferrer"><div data-slot="card" class="rounded-lg border flex flex-col gap-6 shadow-sm py-6 text-[var(--text-primary)] group relative border-[var(--card-border)] bg-[var(--card-background)] hover:bg-[var(--hover-background)] transition-all duration-300 overflow-hidden"><div class="absolute -inset-[1px] bg-gradient-to-r from-blue-500/20 via-cyan-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition duration-500 blur-sm"></div><div data-slot="card-header" class="@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 relative"><div class="space-y-2"><div class="flex items-start justify-between"><div data-slot="card-title" class="font-semibold text-lg group-hover:text-[var(--text-primary)] transition-colors flex items-center gap-2">Need of Context API (or Similar State Management Tools)<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:translate-x-1"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div></div><div data-slot="card-description" class="text-muted-foreground text-sm line-clamp-2">Context API is a react structure enabling us to exchange information across all levels of a react application, think of it as a...</div><div class="flex items-center gap-3 text-sm text-[var(--text-muted)]"><span>Mar 18, 2024</span></div></div></div></div></a></div></div></section></div></div></div></div></div><div class="relative w-full"><div class="max-w-3xl sm:w-3/4 mx-auto px-6"><footer class="py-8 border-t border-[var(--card-border)]" style="opacity: 1; transform: none;"><div class="flex items-center justify-between"><p class="text-xs text-[var(--text-muted)]">Chaitanya Bajpai</p><div class="flex items-center gap-4"><a href="https://x.com/cbajpai7" target="_blank" rel="noopener noreferrer" class="text-[var(--text-secondary)] hover:text-[var(--text-primary)] transition-colors" aria-label="Twitter" style="opacity: 1; transform: none;"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-twitter"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg></a><a href="https://cal.com/chaitanya-bajpai" target="_blank" rel="noopener noreferrer" class="text-[var(--text-secondary)] hover:text-[var(--text-primary)] transition-colors" aria-label="Schedule a call" style="opacity: 1; transform: none;"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg></a><a href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer" class="text-[var(--text-secondary)] hover:text-[var(--text-primary)] transition-colors" aria-label="Email" style="opacity: 1; transform: none;"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail"><rect width="20" height="16" x="2" y="4" rx="2"></rect><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg></a><a href="https://github.com/cb7chaitanya" target="_blank" rel="noopener noreferrer" class="text-[var(--text-secondary)] hover:text-[var(--text-primary)] transition-colors" aria-label="GitHub" style="opacity: 1; transform: none;"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-github"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg></a></div></div></footer></div></div></main><!--$--><!--/$--><!--$--><!--/$--><script src="./Chaitanya Bajpai_files/webpack-c69c9ac8f19d6745.js.download" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[6169,[\"177\",\"static/chunks/app/layout-dd0f9af6f8e573eb.js\"],\"ThemeProvider\"]\n3:I[831,[],\"\"]\n4:I[8307,[],\"\"]\n5:I[2767,[\"177\",\"static/chunks/app/layout-dd0f9af6f8e573eb.js\"],\"Analytics\"]\n6:I[7239,[\"355\",\"static/chunks/355-001cf980e6007590.js\",\"974\",\"static/chunks/app/page-e2502717036d7a31.js\"],\"default\"]\n7:I[1690,[\"355\",\"static/chunks/355-001cf980e6007590.js\",\"974\",\"static/chunks/app/page-e2502717036d7a31.js\"],\"TracingBeam\"]\n8:I[8952,[\"355\",\"static/chunks/355-001cf980e6007590.js\",\"974\",\"static/chunks/app/page-e2502717036d7a31.js\"],\"default\"]\n9:I[7778,[\"355\",\"static/chunks/355-001cf980e6007590.js\",\"974\",\"static/chunks/app/page-e2502717036d7a31.js\"],\"default\"]\na:I[3501,[\"355\",\"static/chunks/355-001cf980e6007590.js\",\"974\",\"static/chunks/app/page-e2502717036d7a31.js\"],\"default\"]\nb:I[9582,[\"355\",\"static/chunks/355-001cf980e6007590.js\",\"974\",\"static/chunks/app/page-e2502717036d7a31.js\"],\"default\"]\nc:I[9757,[],\"MetadataBoundary\"]\ne:I[9757,[],\"OutletBoundary\"]\n11:I[8475,[],\"AsyncMetadataOutlet\"]\n13:I[9757,[],\"ViewportBoundary\"]\n15:I[8458,[],\"\"]\n:HL[\"/_next/static/media/66f30814ff6d7cdf.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/media/e11418ac562b8ac1-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/css/ee9329db790c09cb.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"PHE02vYc_lIQYvMfxzuIN\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ee9329db790c09cb.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"className\":\"__variable_3a0388 __variable_c1e5c9\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_3a0388\",\"children\":[\"$\",\"$L2\",null,{\"attribute\":\"data-theme\",\"defaultTheme\":\"dark\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}],[\"$\",\"$L5\",null,{}]]}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"main\",null,{\"className\":\"relative min-h-screen w-full flex flex-col\",\"children\":[[\"$\",\"div\",null,{\"className\":\"fixed inset-0 bg-[var(--background)] w-full\"}],[\"$\",\"div\",null,{\"className\":\"fixed inset-0\",\"children\":[[\"$\",\"div\",null,{\"className\":\"absolute inset-0 bg-gradient-to-tr from-blue-500/[0.03] via-transparent to-purple-500/[0.03]\"}],[\"$\",\"div\",null,{\"className\":\"absolute inset-0 bg-[linear-gradient(to_right,var(--card-border)_1px,transparent_1px),linear-gradient(to_bottom,var(--card-border)_1px,transparent_1px)] bg-[size:44px_44px]\"}]]}],[\"$\",\"div\",null,{\"className\":\"relative w-full flex-grow\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-3xl sm:w-3/4 mx-auto px-6\",\"children\":[[\"$\",\"$L6\",null,{}],[\"$\",\"div\",null,{\"className\":\"max-w-2xl mx-auto\",\"children\":[\"$\",\"$L7\",null,{\"className\":\"h-fit\",\"children\":[[\"$\",\"$L8\",null,{}],[\"$\",\"$L9\",null,{}],[\"$\",\"$La\",null,{}]]}]}]]}]}],[\"$\",\"div\",null,{\"className\":\"relative w-full\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-3xl sm:w-3/4 mx-auto px-6\",\"children\":[\"$\",\"$Lb\",null,{}]}]}]]}],[\"$\",\"$Lc\",null,{\"children\":\"$Ld\"}],null,[\"$\",\"$Le\",null,{\"children\":[\"$Lf\",\"$L10\",[\"$\",\"$L11\",null,{\"promise\":\"$@12\"}]]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"kgUKvir0BD-2g8_Hyh8yg\",{\"children\":[[\"$\",\"$L13\",null,{\"children\":\"$L14\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$15\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"16:\"$Sreact.suspense\"\n17:I[8475,[],\"AsyncMetadata\"]\nd:[\"$\",\"$16\",null,{\"fallback\":null,\"children\":[\"$\",\"$L17\",null,{\"promise\":\"$@18\"}]}]\n"])</script><script>self.__next_f.push([1,"10:null\n"])</script><script>self.__next_f.push([1,"14:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nf:null\n"])</script><script>self.__next_f.push([1,"18:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Chaitanya Bajpai\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Full Stack Blockchain Developer\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"1920x1080\"}]],\"error\":null,\"digest\":\"$undefined\"}\n12:{\"metadata\":\"$18:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script><next-route-announcer style="position: absolute;"><template shadowrootmode="open"><div aria-live="assertive" id="__next-route-announcer__" role="alert" style="position: absolute; border: 0px; height: 1px; margin: -1px; padding: 0px; width: 1px; clip: rect(0px, 0px, 0px, 0px); overflow: hidden; white-space: nowrap; overflow-wrap: normal;"></div></template></next-route-announcer></body></html>