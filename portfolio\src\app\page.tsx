import { Hero } from '@/components/Hero';
import { Experience } from '@/components/Experience';
import { Projects } from '@/components/Projects';
import { Skills } from '@/components/Skills';
import { Education } from '@/components/Education';
import { Leadership } from '@/components/Leadership';
import { Contact } from '@/components/Contact';

export default function Home() {
  return (
    <main className="relative min-h-screen w-full flex flex-col bg-black">
      {/* Background Effects */}
      <div className="fixed inset-0">
        <div className="absolute inset-0 bg-gradient-to-tr from-blue-500/[0.03] via-transparent to-purple-500/[0.03]"></div>
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#232323_1px,transparent_1px),linear-gradient(to_bottom,#232323_1px,transparent_1px)] bg-[size:44px_44px]"></div>
      </div>

      {/* Content */}
      <div className="relative w-full flex-grow">
        <div className="max-w-4xl mx-auto px-6">
          <Hero />

          <div className="max-w-4xl mx-auto">
            <Experience />
            <Projects />
            <Skills />
            <Education />
            <Leadership />
            <Contact />
          </div>
        </div>
      </div>
    </main>
  );
}
