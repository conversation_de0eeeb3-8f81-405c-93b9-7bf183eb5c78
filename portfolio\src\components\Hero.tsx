import React from 'react';
import { G<PERSON><PERSON>, Linkedin, Mail, Phone } from 'lucide-react';
import { Button } from './ui';

export const Hero: React.FC = () => {
  return (
    <section>
      <div className="flex justify-between items-start mb-8 pt-20">
        {/* Profile Image */}
        <div className="relative w-[128px] h-[128px] rounded-2xl overflow-hidden">
          <div className="h-28 w-28 rounded-3xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-4xl font-bold">
            SR
          </div>
        </div>
        
        {/* Social Links */}
        <div className="flex items-center gap-6">
          <Button
            variant="ghost"
            size="icon"
            href="https://github.com/dorddis"
            target="_blank"
            rel="noopener noreferrer"
            className="w-10 h-10 hover:bg-[var(--hover-background)] rounded-lg transition-colors"
          >
            <Github size={20} />
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            href="https://linkedin.com/in/dorddis"
            target="_blank"
            rel="noopener noreferrer"
            className="w-10 h-10 hover:bg-[var(--hover-background)] rounded-lg transition-colors"
          >
            <Linkedin size={20} />
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            href="mailto:<EMAIL>"
            className="w-10 h-10 hover:bg-[var(--hover-background)] rounded-lg transition-colors"
          >
            <Mail size={20} />
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            href="tel:+919356252711"
            className="w-10 h-10 hover:bg-[var(--hover-background)] rounded-lg transition-colors"
          >
            <Phone size={20} />
          </Button>
        </div>
      </div>
      
      {/* Hero Content */}
      <h1 className="text-[32px] leading-none font-medium text-[var(--text-primary)] mb-2">
        Hi, I'm Siddharth
      </h1>
      
      <p className="text-[var(--text-secondary)] text-base mb-4">
        22, Mumbai | Software Engineer | AI/ML, Python, Backend, Cloud
      </p>
      
      <p className="text-[var(--text-secondary)] max-w-xl">
        I'm a Software Engineer crafting cutting-edge AI/ML solutions and scalable backend systems. 
        From building distributed web scraping platforms to developing interactive fitness analytics, 
        I turn complex technical challenges into user-friendly experiences. Available for U.S. remote roles 
        with 9 AM EST - 6 PM IST overlap.
      </p>
    </section>
  );
};
