import React from 'react';

interface TimelineProps {
  children: React.ReactNode;
  className?: string;
}

export const Timeline: React.FC<TimelineProps> = ({ children, className = '' }) => {
  return (
    <div className={`relative mx-auto w-full max-w-4xl h-fit ${className}`}>
      {/* Timeline line */}
      <div className="absolute top-3 -left-4 md:-left-20 hidden md:block">
        <div className="border-neutral-200 ml-[27px] flex h-4 w-4 items-center justify-center rounded-full border shadow-sm"
             style={{boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px'}}>
          <div className="h-2 w-2 rounded-full border border-neutral-300 bg-white" 
               style={{backgroundColor: 'rgb(16, 185, 129)', borderColor: 'rgb(5, 150, 105)'}}></div>
        </div>
        <svg viewBox="0 0 20 3140" width="20" height="3140" className="ml-4 block" aria-hidden="true">
          <path d="M 1 0V -36 l 18 24 V 2512 l -18 24V 3140" fill="none" stroke="#9091A0" strokeOpacity="0.16"></path>
          <path d="M 1 0V -36 l 18 24 V 2512 l -18 24V 3140" fill="none" stroke="url(#gradient)" strokeWidth="1.25" className="motion-reduce:hidden"></path>
          <defs>
            <linearGradient id="gradient" gradientUnits="userSpaceOnUse" x1="0" x2="0" y1="2844.2869344506103" y2="2140.7415509546313">
              <stop stopColor="#18CCFC" stopOpacity="0"></stop>
              <stop stopColor="#18CCFC"></stop>
              <stop offset="0.325" stopColor="#6344F5"></stop>
              <stop offset="1" stopColor="#AE48FF" stopOpacity="0"></stop>
            </linearGradient>
          </defs>
        </svg>
      </div>
      <div className="space-y-6">
        {children}
      </div>
    </div>
  );
};

interface TimelineItemProps {
  children: React.ReactNode;
  className?: string;
}

export const TimelineItem: React.FC<TimelineItemProps> = ({ children, className = '' }) => {
  return (
    <div className={`relative ${className}`} style={{opacity: 1, transform: 'none'}}>
      {children}
    </div>
  );
};
