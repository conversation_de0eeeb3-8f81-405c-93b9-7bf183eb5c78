"use client"
import React from 'react';
import { ExternalLink, Github } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardTitle, CardDescription, Badge, BadgeGroup, Tabs, TabsList, TabsTrigger, TabsContent } from './ui';

const personalProjects = [
  {
    id: 1,
    title: "Gymzy AI Fitness Analytics & Social Media Platform",
    description: "Interactive AI-based fitness analytics and workout logging platform with social integration",
    status: "Live",
    statusColor: "green",
    features: [
      "Real-time AI-supported workout logging",
      "Personalized muscle activation analytics", 
      "Interactive SVG visualizations",
      "40% improvement in user engagement"
    ],
    technologies: ["Next.js", "TypeScript", "Node.js", "AI Integration"],
    links: [
      { label: "Live Demo", url: "https://gymzy.vercel.app", icon: ExternalLink },
      { label: "GitHub", url: "https://github.com/dorddis/gymzy", icon: Gith<PERSON> }
    ],
    gradient: "from-purple-600/20 via-pink-600/20 to-red-600/20"
  },
  {
    id: 2,
    title: "EggyPro E-commerce Platform",
    description: "Modern e-commerce frontend with AI-powered customer support",
    status: "Live",
    statusColor: "green",
    features: [
      "AI-powered FAQ assistant using Google's Gemini AI",
      "90% reduction in customer support response time",
      "95% mobile compatibility",
      "Sub-1 second page load times"
    ],
    technologies: ["Next.js 15", "TypeScript", "Google Gemini AI", "Genkit"],
    links: [
      { label: "Live Demo", url: "https://eggypro.com", icon: ExternalLink },
      { label: "GitHub", url: "https://github.com/dorddis/eggypro", icon: Github }
    ],
    gradient: "from-cyan-600/20 via-blue-600/20 to-indigo-600/20"
  },
  {
    id: 3,
    title: "AI-assisted Coding and Debugging",
    description: "Accelerated software development through innovative AI model implementation",
    status: "Ongoing",
    statusColor: "blue",
    features: [
      "50% reduction in debugging time",
      "150% improvement in development efficiency",
      "Implementation of o1, o3, and o4-mini models",
      "Advanced prompt engineering techniques"
    ],
    technologies: ["AI Models (o1, o3, o4-mini)", "Prompt Engineering", "Aider CLI"],
    links: [
      { label: "GitHub", url: "https://github.com/dorddis", icon: Github }
    ],
    gradient: "from-green-600/20 via-emerald-600/20 to-teal-600/20"
  }
];

const professionalProjects = [
  {
    id: 1,
    title: "India's First Electoral-Sampling Platform",
    description: "Fully streamlined platform for electoral data collection and analysis",
    status: "Deployed",
    statusColor: "green",
    features: [
      "End-to-end R&D and full-stack implementation",
      "Distributed web scraping system on AWS",
      "9600 man-days of work automation",
      "97% accuracy in data extraction"
    ],
    technologies: ["Python", "AWS EC2", "AWS S3", "Selenium Hub", "OCR"],
    links: [
      { label: "Platform", url: "https://edownloaders.com", icon: ExternalLink }
    ],
    gradient: "from-blue-600/20 via-indigo-600/20 to-purple-600/20"
  }
];

export const Projects: React.FC = () => {
  return (
    <section className="py-20">
      <h2 className="text-2xl font-medium mb-3 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text">
        Projects
      </h2>
      <p className="text-gray-400 mb-8">
        A collection of my work spanning from AI/ML applications to full-stack projects, 
        both personal and professional.
      </p>
      
      <Tabs defaultValue="personal">
        <TabsList>
          <TabsTrigger value="personal">Personal Projects</TabsTrigger>
          <TabsTrigger value="professional">Professional Work</TabsTrigger>
        </TabsList>
        
        <TabsContent value="personal">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 min-h-[400px]">
            {personalProjects.map((project) => (
              <ProjectCard key={project.id} project={project} />
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="professional">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 min-h-[400px]">
            {professionalProjects.map((project) => (
              <ProjectCard key={project.id} project={project} />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </section>
  );
};

interface ProjectCardProps {
  project: {
    title: string;
    description: string;
    status: string;
    statusColor: string;
    features: string[];
    technologies: string[];
    links: Array<{ label: string; url: string; icon: any }>;
    gradient: string;
  };
}

const ProjectCard: React.FC<ProjectCardProps> = ({ project }) => {
  return (
    <Card className="group relative" hover gradient>
      <div className={`absolute -inset-[1px] bg-gradient-to-r ${project.gradient} opacity-0 group-hover:opacity-100 transition duration-500 blur-sm`}></div>
      
      <div className="relative">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <CardTitle className="group-hover:text-[var(--text-primary)] transition-colors">
                  {project.title}
                </CardTitle>
                <ExternalLink className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:translate-x-1" />
              </div>
              <CardDescription className="line-clamp-2">
                {project.description}
              </CardDescription>
            </div>
            
            <Badge variant={project.statusColor === 'green' ? 'status' : 'secondary'}>
              {project.status}
            </Badge>
          </div>
          
          {/* Features */}
          <div className="mt-4">
            <h4 className="text-sm font-medium text-[var(--text-primary)] mb-2">Key Features:</h4>
            <ul className="text-sm text-[var(--text-muted)] space-y-1">
              {project.features.map((feature, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-blue-400 mt-1">•</span>
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Links */}
          {project.links.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-4">
              {project.links.map((link, index) => (
                <Badge 
                  key={index}
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  variant="outline"
                  className="flex items-center gap-1.5"
                >
                  <link.icon className="w-3 h-3" />
                  <span className="text-xs">{link.label}</span>
                </Badge>
              ))}
            </div>
          )}
        </CardHeader>
        
        <CardContent>
          <BadgeGroup>
            {project.technologies.map((tech, index) => (
              <Badge key={index}>{tech}</Badge>
            ))}
          </BadgeGroup>
        </CardContent>
      </div>
    </Card>
  );
};
