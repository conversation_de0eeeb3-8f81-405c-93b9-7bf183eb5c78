import React from 'react';
import { Users, Megaphone, Trophy, Star } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardTitle, CardDescription, Badge } from './ui';

const leadershipActivities = [
  {
    id: 1,
    title: "E-Cell, IIIT Pune - Marketing Associate",
    icon: Megaphone,
    color: "blue",
    description: "Led marketing initiatives and vendor communications for major college events",
    achievements: [
      "Pitched sponsorships for E-Summit 2k23 with 20,000+ participants",
      "Managed vendor communications and negotiations",
      "Contributed to E-Summit 2k22 and Ideathon 2k22 events",
      "Implemented automated payment verification systems",
      "Coordinated project management and event logistics"
    ],
    impact: "Successfully secured sponsorships and managed large-scale event operations"
  },
  {
    id: 2,
    title: "Hackathon Leadership",
    icon: Trophy,
    color: "yellow",
    description: "Led development teams in competitive programming and innovation challenges",
    achievements: [
      "Led team to 4th place in western region (Solving For India Hackathon)",
      "Competed among 2000+ teams nationwide",
      "Developed Blockchain health-record NFTs solution",
      "Selected by Google for experience sharing session",
      "Demonstrated technical leadership and project management"
    ],
    impact: "Showcased technical innovation and team leadership capabilities"
  },
  {
    id: 3,
    title: "Class Representative - Batch of '25",
    icon: Users,
    color: "green",
    description: "Organized and facilitated campus initiatives as student representative",
    achievements: [
      "Represented student interests in academic committees",
      "Organized campus initiatives and student activities",
      "Facilitated communication between students and faculty",
      "Coordinated batch events and academic discussions",
      "Maintained high academic standards (8.14/10 CGPA)"
    ],
    impact: "Enhanced student experience and academic environment"
  },
  {
    id: 4,
    title: "Technical Innovation & Mentorship",
    icon: Star,
    color: "purple",
    description: "Contributed to technical community through innovation and knowledge sharing",
    achievements: [
      "Published academic research with significant improvements",
      "Mentored junior students in programming and development",
      "Contributed to open-source projects and technical communities",
      "Implemented AI-assisted development workflows",
      "Shared expertise in web scraping and automation"
    ],
    impact: "Advanced technical knowledge and community contribution"
  }
];

export const Leadership: React.FC = () => {
  return (
    <section className="py-20">
      <h2 className="text-2xl font-medium mb-3 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text">
        Leadership & Activities
      </h2>
      <p className="text-[var(--text-muted)] mb-8">
        My leadership experience, community involvement, and contributions to 
        student organizations, hackathons, and technical initiatives.
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {leadershipActivities.map((activity) => (
          <Card key={activity.id} hover className="h-fit">
            <CardHeader>
              <div className="flex items-start gap-4">
                <div className="p-2 rounded-lg bg-[var(--hover-background)]">
                  <activity.icon className={`w-5 h-5 text-${activity.color}-400`} />
                </div>
                
                <div className="space-y-1.5 flex-grow">
                  <CardTitle className="text-lg">{activity.title}</CardTitle>
                  <CardDescription>{activity.description}</CardDescription>
                  
                  {/* Impact Badge */}
                  <Badge variant="secondary" className="mt-2">
                    {activity.impact}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-[var(--text-primary)]">Key Contributions:</h4>
                <ul className="space-y-2">
                  {activity.achievements.map((achievement, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm text-[var(--text-muted)]">
                      <span className={`text-${activity.color}-400 mt-1`}>•</span>
                      <span>{achievement}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      {/* Summary Stats */}
      <div className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center p-4 rounded-lg bg-[var(--card-background)] border border-[var(--card-border)]">
          <div className="text-2xl font-bold text-blue-400">20,000+</div>
          <div className="text-sm text-[var(--text-muted)]">Event Participants</div>
        </div>
        <div className="text-center p-4 rounded-lg bg-[var(--card-background)] border border-[var(--card-border)]">
          <div className="text-2xl font-bold text-yellow-400">4th</div>
          <div className="text-sm text-[var(--text-muted)]">Hackathon Rank</div>
        </div>
        <div className="text-center p-4 rounded-lg bg-[var(--card-background)] border border-[var(--card-border)]">
          <div className="text-2xl font-bold text-green-400">2000+</div>
          <div className="text-sm text-[var(--text-muted)]">Competing Teams</div>
        </div>
        <div className="text-center p-4 rounded-lg bg-[var(--card-background)] border border-[var(--card-border)]">
          <div className="text-2xl font-bold text-purple-400">8.14</div>
          <div className="text-sm text-[var(--text-muted)]">CGPA/10</div>
        </div>
      </div>
    </section>
  );
};
