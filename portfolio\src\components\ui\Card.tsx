import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  gradient?: boolean;
  onClick?: () => void;
}

export const Card: React.FC<CardProps> = ({ 
  children, 
  className = '', 
  hover = true, 
  gradient = false,
  onClick 
}) => {
  const baseClasses = `
    rounded-lg border flex flex-col gap-6 shadow-sm py-6 
    text-[var(--text-primary)] group relative 
    border-[var(--card-border)] bg-[var(--card-background)]
    transition-all duration-300 overflow-hidden
  `;
  
  const hoverClasses = hover ? 'hover:bg-[var(--hover-background)] cursor-pointer' : '';
  const gradientClasses = gradient ? `
    before:absolute before:-inset-[1px] before:bg-gradient-to-r 
    before:from-blue-500/20 before:via-cyan-500/20 before:to-transparent 
    before:opacity-0 before:group-hover:opacity-100 before:transition 
    before:duration-500 before:blur-sm
  ` : '';

  return (
    <div 
      className={`${baseClasses} ${hoverClasses} ${gradientClasses} ${className}`}
      onClick={onClick}
    >
      {gradient && <div className="absolute left-0 top-0 h-full w-1 bg-gradient-to-b from-blue-500/20 via-cyan-500/20 to-transparent"></div>}
      {children}
    </div>
  );
};

interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

export const CardHeader: React.FC<CardHeaderProps> = ({ children, className = '' }) => {
  return (
    <div className={`@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 pb-4 ${className}`}>
      {children}
    </div>
  );
};

interface CardContentProps {
  children: React.ReactNode;
  className?: string;
}

export const CardContent: React.FC<CardContentProps> = ({ children, className = '' }) => {
  return (
    <div className={`px-6 pt-0 pb-4 ${className}`}>
      {children}
    </div>
  );
};

interface CardTitleProps {
  children: React.ReactNode;
  className?: string;
}

export const CardTitle: React.FC<CardTitleProps> = ({ children, className = '' }) => {
  return (
    <div className={`font-semibold text-lg text-[var(--text-primary)] ${className}`}>
      {children}
    </div>
  );
};

interface CardDescriptionProps {
  children: React.ReactNode;
  className?: string;
}

export const CardDescription: React.FC<CardDescriptionProps> = ({ children, className = '' }) => {
  return (
    <div className={`text-sm text-[var(--text-muted)] ${className}`}>
      {children}
    </div>
  );
};
