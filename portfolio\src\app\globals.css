@import "tailwindcss";

:root {
  /* Dark theme colors */
  --background: #0a0a0a;
  --foreground: #ededed;
  --text-primary: #fff;
  --text-secondary: #8b8b8b;
  --text-muted: #666;
  --card-background: #1a1a1a;
  --card-border: #232323;
  --hover-background: #242424;

  /* Color palette */
  --color-red-600: oklch(57.7% .245 27.325);
  --color-green-400: oklch(79.2% .209 151.711);
  --color-green-600: oklch(62.7% .194 149.214);
  --color-emerald-600: oklch(59.6% .145 163.225);
  --color-teal-600: oklch(60% .118 184.704);
  --color-cyan-500: oklch(71.5% .143 215.221);
  --color-cyan-600: oklch(60.9% .126 221.723);
  --color-blue-400: oklch(70.7% .165 254.624);
  --color-blue-500: oklch(62.3% .214 259.815);
  --color-blue-600: oklch(54.6% .245 262.881);
  --color-indigo-600: oklch(51.1% .262 276.966);
  --color-purple-400: oklch(71.4% .203 305.504);
  --color-purple-500: oklch(62.7% .265 303.9);
  --color-purple-600: oklch(55.8% .288 302.321);
  --color-pink-400: oklch(71.8% .202 349.761);
  --color-pink-600: oklch(59.2% .249 .584);
  --color-neutral-300: oklch(87% 0 0);
  --color-black: #000;
  --color-white: #fff;

  /* Spacing */
  --spacing: .25rem;

  /* Container sizes */
  --container-lg: 32rem;
  --container-xl: 36rem;
  --container-2xl: 42rem;
  --container-3xl: 48rem;
  --container-4xl: 56rem;

  /* Typography */
  --text-xs: .75rem;
  --text-xs--line-height: calc(1 / .75);
  --text-sm: .875rem;
  --text-sm--line-height: calc(1.25 / .875);
  --text-base: 1rem;
  --text-base--line-height: calc(1.5 / 1);
  --text-lg: 1.125rem;
  --text-lg--line-height: calc(1.75 / 1.125);
  --text-xl: 1.25rem;
  --text-xl--line-height: calc(1.75 / 1.25);
  --text-2xl: 1.5rem;
  --text-2xl--line-height: calc(2 / 1.5);
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --leading-relaxed: 1.625;

  /* Border radius */
  --radius-xs: .125rem;
  --radius-md: .375rem;
  --radius-lg: .5rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;

  /* Blur effects */
  --blur-sm: 8px;
  --blur-xl: 24px;

  /* Transitions */
  --default-transition-duration: .15s;
  --default-transition-timing-function: cubic-bezier(.4,0,.2,1);

  /* Fonts */
  --font-geist-sans: "GeistSans", "GeistSans Fallback";
  --font-geist-mono: "GeistMono", ui-monospace, SFMono-Regular, Roboto Mono, Menlo, Monaco, Liberation Mono, DejaVu Sans Mono, Courier New, monospace;
  --default-font-family: var(--font-geist-sans);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-text-muted: var(--text-muted);
  --color-card-background: var(--card-background);
  --color-card-border: var(--card-border);
  --color-hover-background: var(--hover-background);
  --color-blue-400: #60a5fa;
  --color-blue-500: #3b82f6;
  --color-blue-600: #2563eb;
  --color-cyan-400: #22d3ee;
  --color-cyan-500: #06b6d4;
  --color-cyan-600: #0891b2;
  --color-green-400: #4ade80;
  --color-green-500: #22c55e;
  --color-purple-400: #a78bfa;
  --color-purple-500: #8b5cf6;
  --color-yellow-400: #facc15;
  --color-orange-400: #fb923c;
  --color-pink-400: #f472b6;
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

*, ::backdrop, :after, :before {
  box-sizing: border-box;
  border: 0 solid;
  margin: 0;
  padding: 0;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif);
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  tab-size: 4;
  font-feature-settings: normal;
  font-variation-settings: normal;
  -webkit-tap-highlight-color: transparent;
}
