element.style {
}
.flex-grow, .grow {
    flex-grow: 1;
}
.w-full {
    width: 100%;
}
.relative {
    position: relative;
}
*, ::backdrop, :after, :before {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
}
user agent stylesheet
div {
    display: block;
    unicode-bidi: isolate;
}
.__className_3a0388 {
    font-family: <PERSON><PERSON><PERSON><PERSON><PERSON>, GeistSans Fallback;
}
body {
    background: var(--background);
    color: var(--foreground);
    font-family: Arial, Helvetica, sans-serif;
}
style attribute {
    color-scheme: dark;
}
:root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --text-primary: #fff;
    --text-secondary: #8b8b8b;
    --text-muted: #666;
    --card-background: #1a1a1a;
    --card-border: #232323;
    --hover-background: #242424;
}
.__variable_c1e5c9 {
    --font-geist-mono: "<PERSON>eistM<PERSON>", ui-monospace, SFMono-Regular, <PERSON><PERSON>, <PERSON><PERSON>, Monaco, Liberation Mono, <PERSON><PERSON><PERSON>, Courier New, monospace;
}
.__variable_3a0388 {
    --font-geist-sans: "GeistSans", "GeistSans Fallback";
}
:host, html {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
}
:host, :root {
    --color-red-600: oklch(57.7% .245 27.325);
    --color-green-400: oklch(79.2% .209 151.711);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-emerald-600: oklch(59.6% .145 163.225);
    --color-teal-600: oklch(60% .118 184.704);
    --color-cyan-500: oklch(71.5% .143 215.221);
    --color-cyan-600: oklch(60.9% .126 221.723);
    --color-blue-400: oklch(70.7% .165 254.624);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-indigo-600: oklch(51.1% .262 276.966);
    --color-purple-400: oklch(71.4% .203 305.504);
    --color-purple-500: oklch(62.7% .265 303.9);
    --color-purple-600: oklch(55.8% .288 302.321);
    --color-pink-400: oklch(71.8% .202 349.761);
    --color-pink-600: oklch(59.2% .249 .584);
    --color-neutral-300: oklch(87% 0 0);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --leading-relaxed: 1.625;
    --radius-xs: .125rem;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --blur-sm: 8px;
    --blur-xl: 24px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4,0,.2,1);
    --default-font-family: var(--font-geist-sans);
Show all properties (1 more)
}
*, ::backdrop, :after, :before {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
}
*, ::backdrop, :after, :before {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
}
*, ::backdrop, :after, :before {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
