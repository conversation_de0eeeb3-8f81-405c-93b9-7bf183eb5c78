{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/ui/Tabs.tsx"], "sourcesContent": ["\"use client\"\nimport React, { useState } from 'react';\n\ninterface TabsProps {\n  children: React.ReactNode;\n  defaultValue: string;\n  className?: string;\n}\n\ninterface TabsContextType {\n  activeTab: string;\n  setActiveTab: (value: string) => void;\n}\n\nconst TabsContext = React.createContext<TabsContextType | undefined>(undefined);\n\nexport const Tabs: React.FC<TabsProps> = ({ children, defaultValue, className = '' }) => {\n  const [activeTab, setActiveTab] = useState(defaultValue);\n\n  return (\n    <TabsContext.Provider value={{ activeTab, setActiveTab }}>\n      <div className={`flex flex-col gap-2 w-full ${className}`}>\n        {children}\n      </div>\n    </TabsContext.Provider>\n  );\n};\n\ninterface TabsListProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const TabsList: React.FC<TabsListProps> = ({ children, className = '' }) => {\n  return (\n    <div className={`\n      text-muted-foreground h-9 items-center justify-center rounded-lg p-[3px] \n      grid w-full grid-cols-2 mb-8 bg-[var(--card-background)] \n      border-[var(--card-border)] border\n      ${className}\n    `}>\n      {children}\n    </div>\n  );\n};\n\ninterface TabsTriggerProps {\n  children: React.ReactNode;\n  value: string;\n  className?: string;\n}\n\nexport const TabsTrigger: React.FC<TabsTriggerProps> = ({ children, value, className = '' }) => {\n  const context = React.useContext(TabsContext);\n  if (!context) {\n    throw new Error('TabsTrigger must be used within Tabs');\n  }\n\n  const { activeTab, setActiveTab } = context;\n  const isActive = activeTab === value;\n\n  return (\n    <button\n      type=\"button\"\n      onClick={() => setActiveTab(value)}\n      className={`\n        inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 \n        rounded-md border border-transparent px-2 py-1 text-sm font-medium \n        whitespace-nowrap transition-[color,box-shadow] \n        disabled:pointer-events-none disabled:opacity-50\n        ${isActive \n          ? 'bg-[var(--hover-background)] text-[var(--text-primary)] shadow-sm' \n          : 'text-[var(--text-secondary)] hover:text-[var(--text-primary)]'\n        }\n        ${className}\n      `}\n    >\n      {children}\n    </button>\n  );\n};\n\ninterface TabsContentProps {\n  children: React.ReactNode;\n  value: string;\n  className?: string;\n}\n\nexport const TabsContent: React.FC<TabsContentProps> = ({ children, value, className = '' }) => {\n  const context = React.useContext(TabsContext);\n  if (!context) {\n    throw new Error('TabsContent must be used within Tabs');\n  }\n\n  const { activeTab } = context;\n  \n  if (activeTab !== value) {\n    return null;\n  }\n\n  return (\n    <div className={`flex-1 outline-none mt-0 ${className}`}>\n      {children}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;AACA;AADA;;;AAcA,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,aAAa,CAA8B;AAE9D,MAAM,OAA4B,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,EAAE;IAClF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAW;QAAa;kBACrD,cAAA,8OAAC;YAAI,WAAW,CAAC,2BAA2B,EAAE,WAAW;sBACtD;;;;;;;;;;;AAIT;AAOO,MAAM,WAAoC,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IAC5E,qBACE,8OAAC;QAAI,WAAW,CAAC;;;;MAIf,EAAE,UAAU;IACd,CAAC;kBACE;;;;;;AAGP;AAQO,MAAM,cAA0C,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;IACzF,MAAM,UAAU,qMAAA,CAAA,UAAK,CAAC,UAAU,CAAC;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG;IACpC,MAAM,WAAW,cAAc;IAE/B,qBACE,8OAAC;QACC,MAAK;QACL,SAAS,IAAM,aAAa;QAC5B,WAAW,CAAC;;;;;QAKV,EAAE,WACE,sEACA,gEACH;QACD,EAAE,UAAU;MACd,CAAC;kBAEA;;;;;;AAGP;AAQO,MAAM,cAA0C,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;IACzF,MAAM,UAAU,qMAAA,CAAA,UAAK,CAAC,UAAU,CAAC;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,SAAS,EAAE,GAAG;IAEtB,IAAI,cAAc,OAAO;QACvB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,yBAAyB,EAAE,WAAW;kBACpD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}