import React from 'react';
import { Mail, Phone, MapPin, <PERSON><PERSON><PERSON>, <PERSON>edin, Clock, Globe } from 'lucide-react';
import { <PERSON>, CardHeader, CardContent, CardTitle, CardDescription, Button } from './ui';

export const Contact: React.FC = () => {
  return (
    <section className="py-20">
      <h2 className="text-2xl font-medium mb-3 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text">
        Get In Touch
      </h2>
      <p className="text-[var(--text-muted)] mb-8">
        I'm available for U.S. remote roles with excellent timezone overlap. 
        Let's discuss how I can contribute to your team's success.
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Contact Information */}
        <Card hover>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="w-5 h-5 text-blue-400" />
              Contact Information
            </CardTitle>
            <CardDescription>
              Ready to collaborate on exciting projects and opportunities
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <div className="space-y-4">
              {/* Email */}
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-[var(--hover-background)]">
                  <Mail className="w-4 h-4 text-blue-400" />
                </div>
                <div>
                  <div className="font-medium text-[var(--text-primary)]">Email</div>
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-sm text-[var(--text-secondary)] hover:text-[var(--text-primary)] transition-colors"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>
              
              {/* Phone */}
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-[var(--hover-background)]">
                  <Phone className="w-4 h-4 text-green-400" />
                </div>
                <div>
                  <div className="font-medium text-[var(--text-primary)]">Phone</div>
                  <a 
                    href="tel:+919356252711" 
                    className="text-sm text-[var(--text-secondary)] hover:text-[var(--text-primary)] transition-colors"
                  >
                    (+91) 9356252711
                  </a>
                </div>
              </div>
              
              {/* Location */}
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-[var(--hover-background)]">
                  <MapPin className="w-4 h-4 text-purple-400" />
                </div>
                <div>
                  <div className="font-medium text-[var(--text-primary)]">Location</div>
                  <div className="text-sm text-[var(--text-secondary)]">Mumbai, India</div>
                </div>
              </div>
              
              {/* Timezone */}
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-[var(--hover-background)]">
                  <Clock className="w-4 h-4 text-orange-400" />
                </div>
                <div>
                  <div className="font-medium text-[var(--text-primary)]">Availability</div>
                  <div className="text-sm text-[var(--text-secondary)]">
                    9 AM EST - 6 PM IST overlap
                  </div>
                </div>
              </div>
              
              {/* Remote Work */}
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-[var(--hover-background)]">
                  <Globe className="w-4 h-4 text-cyan-400" />
                </div>
                <div>
                  <div className="font-medium text-[var(--text-primary)]">Remote Work</div>
                  <div className="text-sm text-[var(--text-secondary)]">
                    Available for U.S. remote roles
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Social Links & Quick Actions */}
        <Card hover>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Github className="w-5 h-5 text-purple-400" />
              Connect & Collaborate
            </CardTitle>
            <CardDescription>
              Explore my work, connect professionally, or reach out directly
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <div className="space-y-4">
              {/* GitHub */}
              <Button
                variant="outline"
                className="w-full justify-start gap-3 h-12"
                href="https://github.com/dorddis"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Github className="w-5 h-5" />
                <div className="text-left">
                  <div className="font-medium">GitHub</div>
                  <div className="text-xs text-[var(--text-muted)]">View my repositories and contributions</div>
                </div>
              </Button>
              
              {/* LinkedIn */}
              <Button
                variant="outline"
                className="w-full justify-start gap-3 h-12"
                href="https://linkedin.com/in/dorddis"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Linkedin className="w-5 h-5" />
                <div className="text-left">
                  <div className="font-medium">LinkedIn</div>
                  <div className="text-xs text-[var(--text-muted)]">Connect professionally</div>
                </div>
              </Button>
              
              {/* Email CTA */}
              <Button
                className="w-full justify-start gap-3 h-12"
                href="mailto:<EMAIL>?subject=Opportunity Discussion&body=Hi Siddharth,%0D%0A%0D%0AI came across your portfolio and would like to discuss..."
              >
                <Mail className="w-5 h-5" />
                <div className="text-left">
                  <div className="font-medium">Send Email</div>
                  <div className="text-xs opacity-90">Let's discuss opportunities</div>
                </div>
              </Button>
              
              {/* Phone CTA */}
              <Button
                variant="outline"
                className="w-full justify-start gap-3 h-12"
                href="tel:+919356252711"
              >
                <Phone className="w-5 h-5" />
                <div className="text-left">
                  <div className="font-medium">Call Direct</div>
                  <div className="text-xs text-[var(--text-muted)]">Available during overlap hours</div>
                </div>
              </Button>
            </div>
            
            {/* Availability Note */}
            <div className="mt-6 p-4 rounded-lg bg-blue-500/10 border border-blue-500/20">
              <div className="flex items-start gap-2">
                <Clock className="w-4 h-4 text-blue-400 mt-0.5" />
                <div>
                  <div className="text-sm font-medium text-blue-400">Timezone Overlap</div>
                  <div className="text-xs text-[var(--text-muted)] mt-1">
                    Perfect for U.S. remote work with 9 AM EST - 6 PM IST overlap, 
                    ensuring seamless collaboration during business hours.
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Call to Action */}
      <div className="mt-12 text-center">
        <div className="max-w-2xl mx-auto">
          <h3 className="text-xl font-medium text-[var(--text-primary)] mb-4">
            Ready to Build Something Amazing Together?
          </h3>
          <p className="text-[var(--text-muted)] mb-6">
            Whether you're looking for a full-stack developer, AI/ML engineer, or cloud specialist, 
            I'm excited to contribute to innovative projects and drive technical excellence.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              href="mailto:<EMAIL>?subject=Let's Work Together&body=Hi Siddharth,%0D%0A%0D%0AI'm interested in discussing a potential collaboration..."
              className="px-8"
            >
              <Mail className="w-4 h-4 mr-2" />
              Start a Conversation
            </Button>
            <Button
              variant="outline"
              size="lg"
              href="https://linkedin.com/in/dorddis"
              target="_blank"
              rel="noopener noreferrer"
              className="px-8"
            >
              <Linkedin className="w-4 h-4 mr-2" />
              Connect on LinkedIn
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};
