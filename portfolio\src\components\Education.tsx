import React from 'react';
import { GraduationCap, Award, BookOpen, Trophy } from 'lucide-react';
import { <PERSON>, <PERSON>H<PERSON>er, CardContent, CardTitle, CardDescription, Badge, BadgeGroup } from './ui';

export const Education: React.FC = () => {
  return (
    <section className="py-20">
      <h2 className="text-2xl font-medium mb-3 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text">
        Education & Achievements
      </h2>
      <p className="text-[var(--text-muted)] mb-8">
        My academic background, research contributions, and notable achievements 
        in competitive examinations and hackathons.
      </p>
      
      <div className="space-y-6">
        {/* Main Education */}
        <Card gradient hover>
          <CardHeader>
            <div className="flex flex-col sm:flex-row items-start gap-4">
              <div className="p-2 rounded-lg bg-[var(--hover-background)]">
                <GraduationCap className="w-5 h-5 text-blue-400" />
              </div>
              
              <div className="space-y-1.5 flex-grow w-full">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                  <div className="flex flex-wrap items-center gap-2">
                    <CardTitle>Bachelor of Technology - Computer Science and Engineering</CardTitle>
                  </div>
                  <Badge variant="status">Graduated</Badge>
                </div>
                
                <div className="text-sm text-[var(--text-muted)]">
                  Indian Institute of Information Technology, Pune
                </div>
                <div className="text-sm text-[var(--text-muted)]">
                  CGPA: 8.14/10
                </div>
                <CardDescription>
                  Comprehensive computer science education with focus on software engineering, 
                  algorithms, and emerging technologies.
                </CardDescription>
                
                {/* Key Courses */}
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-[var(--text-primary)] mb-2">Key Courses:</h4>
                  <BadgeGroup>
                    {[
                      "C++", "Java", "Python", "Statistics", "Data Structures & Algorithms",
                      "Database Management (SQL)", "OOP", "Machine Learning", "Cloud Computing",
                      "Big Data", "High Performance & Distributed Computing"
                    ].map((course, index) => (
                      <Badge key={index}>{course}</Badge>
                    ))}
                  </BadgeGroup>
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Research */}
        <Card hover>
          <CardHeader>
            <div className="flex flex-col sm:flex-row items-start gap-4">
              <div className="p-2 rounded-lg bg-[var(--hover-background)]">
                <BookOpen className="w-5 h-5 text-green-400" />
              </div>
              
              <div className="space-y-1.5 flex-grow w-full">
                <CardTitle>Academic Research Paper</CardTitle>
                <CardDescription>
                  Deep Learning Model resulting in a 28 dB average PSNR improvement & 36% OCR improvement
                </CardDescription>
                
                <div className="mt-3">
                  <h4 className="text-sm font-medium text-[var(--text-primary)] mb-2">Research Impact:</h4>
                  <ul className="text-sm text-[var(--text-muted)] space-y-1">
                    <li className="flex items-start gap-2">
                      <span className="text-green-400 mt-1">•</span>
                      <span>28 dB average PSNR (Peak Signal-to-Noise Ratio) improvement</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-green-400 mt-1">•</span>
                      <span>36% improvement in OCR (Optical Character Recognition) accuracy</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-green-400 mt-1">•</span>
                      <span>Novel deep learning approach for image processing enhancement</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Achievements */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card hover>
            <CardHeader>
              <div className="flex items-start gap-4">
                <div className="p-2 rounded-lg bg-[var(--hover-background)]">
                  <Trophy className="w-5 h-5 text-yellow-400" />
                </div>
                
                <div className="space-y-1.5">
                  <CardTitle className="text-base">Hackathon Achievement</CardTitle>
                  <CardDescription>
                    4th place in western region - Solving For India Hackathon among 2000+ teams
                  </CardDescription>
                  
                  <div className="mt-3">
                    <ul className="text-sm text-[var(--text-muted)] space-y-1">
                      <li className="flex items-start gap-2">
                        <span className="text-yellow-400 mt-1">•</span>
                        <span>Blockchain health-record NFTs project</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-yellow-400 mt-1">•</span>
                        <span>Selected by Google for experience sharing</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-yellow-400 mt-1">•</span>
                        <span>Led team development and presentation</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>

          <Card hover>
            <CardHeader>
              <div className="flex items-start gap-4">
                <div className="p-2 rounded-lg bg-[var(--hover-background)]">
                  <Award className="w-5 h-5 text-purple-400" />
                </div>
                
                <div className="space-y-1.5">
                  <CardTitle className="text-base">Competitive Achievements</CardTitle>
                  <CardDescription>
                    Outstanding performance in national level competitive examinations
                  </CardDescription>
                  
                  <div className="mt-3">
                    <ul className="text-sm text-[var(--text-muted)] space-y-1">
                      <li className="flex items-start gap-2">
                        <span className="text-purple-400 mt-1">•</span>
                        <span>JEE 2021: 97.9 percentile overall</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-purple-400 mt-1">•</span>
                        <span>JEE 2021 Math: 99.5 percentile</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-purple-400 mt-1">•</span>
                        <span>Intermediate Degree: 87.7% (Bi-Focal Science)</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-purple-400 mt-1">•</span>
                        <span>Class Representative - Batch of '25</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>
        </div>
      </div>
    </section>
  );
};
