{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover?: boolean;\n  gradient?: boolean;\n  onClick?: () => void;\n}\n\nexport const Card: React.FC<CardProps> = ({\n  children,\n  className = '',\n  hover = true,\n  gradient = false,\n  onClick\n}) => {\n  const baseClasses = `\n    rounded-lg border flex flex-col gap-6 shadow-sm py-6\n    text-white group relative\n    border-gray-700 bg-gray-900\n    transition-all duration-300 overflow-hidden\n  `;\n\n  const hoverClasses = hover ? 'hover:bg-gray-800 cursor-pointer' : '';\n  const gradientClasses = gradient ? `\n    before:absolute before:-inset-[1px] before:bg-gradient-to-r\n    before:from-blue-500/20 before:via-cyan-500/20 before:to-transparent\n    before:opacity-0 before:group-hover:opacity-100 before:transition\n    before:duration-500 before:blur-sm\n  ` : '';\n\n  return (\n    <div\n      className={`${baseClasses} ${hoverClasses} ${gradientClasses} ${className}`}\n      onClick={onClick}\n    >\n      {gradient && <div className=\"absolute left-0 top-0 h-full w-1 bg-gradient-to-b from-blue-500/20 via-cyan-500/20 to-transparent\"></div>}\n      {children}\n    </div>\n  );\n};\n\ninterface CardHeaderProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardHeader: React.FC<CardHeaderProps> = ({ children, className = '' }) => {\n  return (\n    <div className={`@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 pb-4 ${className}`}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardContentProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardContent: React.FC<CardContentProps> = ({ children, className = '' }) => {\n  return (\n    <div className={`px-6 pt-0 pb-4 ${className}`}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardTitleProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardTitle: React.FC<CardTitleProps> = ({ children, className = '' }) => {\n  return (\n    <div className={`font-semibold text-lg text-white ${className}`}>\n      {children}\n    </div>\n  );\n};\n\ninterface CardDescriptionProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardDescription: React.FC<CardDescriptionProps> = ({ children, className = '' }) => {\n  return (\n    <div className={`text-sm text-gray-400 ${className}`}>\n      {children}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;;;AAUO,MAAM,OAA4B,CAAC,EACxC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,IAAI,EACZ,WAAW,KAAK,EAChB,OAAO,EACR;IACC,MAAM,cAAc,CAAC;;;;;EAKrB,CAAC;IAED,MAAM,eAAe,QAAQ,qCAAqC;IAClE,MAAM,kBAAkB,WAAW,CAAC;;;;;EAKpC,CAAC,GAAG;IAEJ,qBACE,8OAAC;QACC,WAAW,GAAG,YAAY,CAAC,EAAE,aAAa,CAAC,EAAE,gBAAgB,CAAC,EAAE,WAAW;QAC3E,SAAS;;YAER,0BAAY,8OAAC;gBAAI,WAAU;;;;;;YAC3B;;;;;;;AAGP;AAOO,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IAChF,qBACE,8OAAC;QAAI,WAAW,CAAC,8FAA8F,EAAE,WAAW;kBACzH;;;;;;AAGP;AAOO,MAAM,cAA0C,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IAClF,qBACE,8OAAC;QAAI,WAAW,CAAC,eAAe,EAAE,WAAW;kBAC1C;;;;;;AAGP;AAOO,MAAM,YAAsC,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IAC9E,qBACE,8OAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC5D;;;;;;AAGP;AAOO,MAAM,kBAAkD,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IAC1F,qBACE,8OAAC;QAAI,WAAW,CAAC,sBAAsB,EAAE,WAAW;kBACjD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/ui/Badge.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface BadgeProps {\n  children: React.ReactNode;\n  variant?: 'default' | 'secondary' | 'outline' | 'status';\n  className?: string;\n  href?: string;\n  target?: string;\n  rel?: string;\n}\n\nexport const Badge: React.FC<BadgeProps> = ({ \n  children, \n  variant = 'default', \n  className = '',\n  href,\n  target,\n  rel\n}) => {\n  const baseClasses = `\n    inline-flex items-center justify-center rounded-md border px-2 py-0.5 \n    text-xs font-medium w-fit whitespace-nowrap shrink-0 gap-1 \n    transition-colors overflow-hidden\n  `;\n\n  const variantClasses = {\n    default: `\n      bg-gray-800 border-gray-700\n      hover:bg-gray-700 hover:border-gray-600\n      text-white\n    `,\n    secondary: `\n      bg-blue-500/10 text-blue-400 hover:bg-blue-500/20\n      border-transparent\n    `,\n    outline: `\n      border-gray-700 bg-transparent\n      hover:bg-gray-800 text-gray-300\n    `,\n    status: `\n      bg-green-500/10 text-green-400 hover:bg-green-500/20\n      border-green-500/20\n    `\n  };\n\n  const classes = `${baseClasses} ${variantClasses[variant]} ${className}`;\n\n  if (href) {\n    return (\n      <a \n        href={href} \n        target={target} \n        rel={rel}\n        className={`${classes} hover:no-underline`}\n      >\n        {children}\n      </a>\n    );\n  }\n\n  return (\n    <span className={classes}>\n      {children}\n    </span>\n  );\n};\n\ninterface BadgeGroupProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const BadgeGroup: React.FC<BadgeGroupProps> = ({ children, className = '' }) => {\n  return (\n    <div className={`flex flex-wrap gap-2 ${className}`}>\n      {children}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;AAWO,MAAM,QAA8B,CAAC,EAC1C,QAAQ,EACR,UAAU,SAAS,EACnB,YAAY,EAAE,EACd,IAAI,EACJ,MAAM,EACN,GAAG,EACJ;IACC,MAAM,cAAc,CAAC;;;;EAIrB,CAAC;IAED,MAAM,iBAAiB;QACrB,SAAS,CAAC;;;;IAIV,CAAC;QACD,WAAW,CAAC;;;IAGZ,CAAC;QACD,SAAS,CAAC;;;IAGV,CAAC;QACD,QAAQ,CAAC;;;IAGT,CAAC;IACH;IAEA,MAAM,UAAU,GAAG,YAAY,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW;IAExE,IAAI,MAAM;QACR,qBACE,8OAAC;YACC,MAAM;YACN,QAAQ;YACR,KAAK;YACL,WAAW,GAAG,QAAQ,mBAAmB,CAAC;sBAEzC;;;;;;IAGP;IAEA,qBACE,8OAAC;QAAK,WAAW;kBACd;;;;;;AAGP;AAOO,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IAChF,qBACE,8OAAC;QAAI,WAAW,CAAC,qBAAqB,EAAE,WAAW;kBAChD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface ButtonProps {\n  children: React.ReactNode;\n  variant?: 'default' | 'ghost' | 'outline' | 'link';\n  size?: 'sm' | 'md' | 'lg' | 'icon';\n  className?: string;\n  onClick?: () => void;\n  href?: string;\n  target?: string;\n  rel?: string;\n  disabled?: boolean;\n}\n\nexport const Button: React.FC<ButtonProps> = ({ \n  children, \n  variant = 'default', \n  size = 'md',\n  className = '',\n  onClick,\n  href,\n  target,\n  rel,\n  disabled = false\n}) => {\n  const baseClasses = `\n    inline-flex items-center justify-center rounded-lg font-medium \n    transition-colors focus-visible:outline-none focus-visible:ring-2 \n    focus-visible:ring-ring focus-visible:ring-offset-2 \n    disabled:pointer-events-none disabled:opacity-50\n  `;\n\n  const variantClasses = {\n    default: `\n      bg-white text-black\n      hover:bg-gray-200\n    `,\n    ghost: `\n      hover:bg-gray-800 hover:text-white\n      text-gray-300\n    `,\n    outline: `\n      border border-gray-700 bg-transparent\n      hover:bg-gray-800 hover:text-white\n      text-gray-300\n    `,\n    link: `\n      text-white underline-offset-4 hover:underline\n      bg-transparent\n    `\n  };\n\n  const sizeClasses = {\n    sm: 'h-8 px-3 text-xs',\n    md: 'h-10 px-4 py-2',\n    lg: 'h-11 px-8',\n    icon: 'h-10 w-10'\n  };\n\n  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;\n\n  if (href) {\n    return (\n      <a \n        href={href} \n        target={target} \n        rel={rel}\n        className={`${classes} no-underline`}\n      >\n        {children}\n      </a>\n    );\n  }\n\n  return (\n    <button \n      className={classes}\n      onClick={onClick}\n      disabled={disabled}\n    >\n      {children}\n    </button>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAcO,MAAM,SAAgC,CAAC,EAC5C,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,OAAO,EACP,IAAI,EACJ,MAAM,EACN,GAAG,EACH,WAAW,KAAK,EACjB;IACC,MAAM,cAAc,CAAC;;;;;EAKrB,CAAC;IAED,MAAM,iBAAiB;QACrB,SAAS,CAAC;;;IAGV,CAAC;QACD,OAAO,CAAC;;;IAGR,CAAC;QACD,SAAS,CAAC;;;;IAIV,CAAC;QACD,MAAM,CAAC;;;IAGP,CAAC;IACH;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,MAAM,UAAU,GAAG,YAAY,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;IAE7F,IAAI,MAAM;QACR,qBACE,8OAAC;YACC,MAAM;YACN,QAAQ;YACR,KAAK;YACL,WAAW,GAAG,QAAQ,aAAa,CAAC;sBAEnC;;;;;;IAGP;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,SAAS;QACT,UAAU;kBAET;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/ui/Timeline.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface TimelineProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const Timeline: React.FC<TimelineProps> = ({ children, className = '' }) => {\n  return (\n    <div className={`relative mx-auto w-full max-w-4xl h-fit ${className}`}>\n      {/* Timeline line */}\n      <div className=\"absolute top-3 -left-4 md:-left-20 hidden md:block\">\n        <div className=\"border-gray-600 ml-[27px] flex h-4 w-4 items-center justify-center rounded-full border shadow-sm\">\n          <div className=\"h-2 w-2 rounded-full border border-green-500 bg-green-400\"></div>\n        </div>\n        <svg viewBox=\"0 0 20 800\" width=\"20\" height=\"800\" className=\"ml-4 block\" aria-hidden=\"true\">\n          <path d=\"M 1 0V -36 l 18 24 V 600 l -18 24V 800\" fill=\"none\" stroke=\"#374151\" strokeOpacity=\"0.5\"></path>\n          <path d=\"M 1 0V -36 l 18 24 V 600 l -18 24V 800\" fill=\"none\" stroke=\"url(#gradient)\" strokeWidth=\"1.25\" className=\"motion-reduce:hidden\"></path>\n          <defs>\n            <linearGradient id=\"gradient\" gradientUnits=\"userSpaceOnUse\" x1=\"0\" x2=\"0\" y1=\"0\" y2=\"800\">\n              <stop stopColor=\"#18CCFC\" stopOpacity=\"0\"></stop>\n              <stop stopColor=\"#18CCFC\"></stop>\n              <stop offset=\"0.325\" stopColor=\"#6344F5\"></stop>\n              <stop offset=\"1\" stopColor=\"#AE48FF\" stopOpacity=\"0\"></stop>\n            </linearGradient>\n          </defs>\n        </svg>\n      </div>\n      <div className=\"space-y-6\">\n        {children}\n      </div>\n    </div>\n  );\n};\n\ninterface TimelineItemProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const TimelineItem: React.FC<TimelineItemProps> = ({ children, className = '' }) => {\n  return (\n    <div className={`relative ${className}`} style={{opacity: 1, transform: 'none'}}>\n      {children}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;AAOO,MAAM,WAAoC,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IAC5E,qBACE,8OAAC;QAAI,WAAW,CAAC,wCAAwC,EAAE,WAAW;;0BAEpE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,SAAQ;wBAAa,OAAM;wBAAK,QAAO;wBAAM,WAAU;wBAAa,eAAY;;0CACnF,8OAAC;gCAAK,GAAE;gCAAyC,MAAK;gCAAO,QAAO;gCAAU,eAAc;;;;;;0CAC5F,8OAAC;gCAAK,GAAE;gCAAyC,MAAK;gCAAO,QAAO;gCAAiB,aAAY;gCAAO,WAAU;;;;;;0CAClH,8OAAC;0CACC,cAAA,8OAAC;oCAAe,IAAG;oCAAW,eAAc;oCAAiB,IAAG;oCAAI,IAAG;oCAAI,IAAG;oCAAI,IAAG;;sDACnF,8OAAC;4CAAK,WAAU;4CAAU,aAAY;;;;;;sDACtC,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;4CAAK,QAAO;4CAAQ,WAAU;;;;;;sDAC/B,8OAAC;4CAAK,QAAO;4CAAI,WAAU;4CAAU,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAKzD,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;AAOO,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IACpF,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;QAAE,OAAO;YAAC,SAAS;YAAG,WAAW;QAAM;kBAC3E;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/ui/Tabs.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Tabs = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Tabs.tsx <module evaluation>\",\n    \"Tabs\",\n);\nexport const TabsContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsContent() from the server but TabsContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Tabs.tsx <module evaluation>\",\n    \"TabsContent\",\n);\nexport const TabsList = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsList() from the server but <PERSON><PERSON>List is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Tabs.tsx <module evaluation>\",\n    \"TabsList\",\n);\nexport const TabsTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsTrigger() from the server but TabsTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Tabs.tsx <module evaluation>\",\n    \"TabsTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,4DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,4DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4DACA", "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/ui/Tabs.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Tabs = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Tabs.tsx\",\n    \"Tabs\",\n);\nexport const TabsContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsContent() from the server but TabsContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Tabs.tsx\",\n    \"TabsContent\",\n);\nexport const TabsList = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsList() from the server but TabsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Tabs.tsx\",\n    \"TabsList\",\n);\nexport const TabsTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call TabsTrigger() from the server but TabsTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Tabs.tsx\",\n    \"TabsTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,wCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,wCACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,wCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,wCACA", "debugId": null}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/ui/index.ts"], "sourcesContent": ["export { Card, <PERSON><PERSON><PERSON>er, CardContent, CardTitle, CardDescription } from './Card';\nexport { Badge, BadgeGroup } from './Badge';\nexport { Button } from './Button';\nexport { Timeline, TimelineItem } from './Timeline';\nexport { Ta<PERSON>, Tabs<PERSON>ist, TabsTrigger, TabsContent } from './Tabs';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/Hero.tsx"], "sourcesContent": ["import React from 'react';\nimport { Gith<PERSON>, Linkedin, Mail, Phone } from 'lucide-react';\nimport { Button } from './ui';\n\nexport const Hero: React.FC = () => {\n  return (\n    <section>\n      <div className=\"flex justify-between items-start mb-8 pt-20\">\n        {/* Profile Image */}\n        <div className=\"relative w-[128px] h-[128px] rounded-2xl overflow-hidden\">\n          <div className=\"h-28 w-28 rounded-3xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-4xl font-bold\">\n            SR\n          </div>\n        </div>\n        \n        {/* Social Links */}\n        <div className=\"flex items-center gap-6\">\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            href=\"https://github.com/dorddis\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"w-10 h-10 hover:bg-gray-800 rounded-lg transition-colors\"\n          >\n            <Github size={20} />\n          </Button>\n          \n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            href=\"https://linkedin.com/in/dorddis\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"w-10 h-10 hover:bg-gray-800 rounded-lg transition-colors\"\n          >\n            <Linkedin size={20} />\n          </Button>\n          \n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            href=\"mailto:<EMAIL>\"\n            className=\"w-10 h-10 hover:bg-gray-800 rounded-lg transition-colors\"\n          >\n            <Mail size={20} />\n          </Button>\n          \n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            href=\"tel:+919356252711\"\n            className=\"w-10 h-10 hover:bg-gray-800 rounded-lg transition-colors\"\n          >\n            <Phone size={20} />\n          </Button>\n        </div>\n      </div>\n      \n      {/* Hero Content */}\n      <h1 className=\"text-[32px] leading-none font-medium text-white mb-2\">\n        Hi, I'm Siddharth\n      </h1>\n\n      <p className=\"text-gray-300 text-base mb-4\">\n        22, Mumbai | Software Engineer | AI/ML, Python, Backend, Cloud\n      </p>\n\n      <p className=\"text-gray-400 max-w-xl\">\n        I'm a Software Engineer crafting cutting-edge AI/ML solutions and scalable backend systems.\n        From building distributed web scraping platforms to developing interactive fitness analytics,\n        I turn complex technical challenges into user-friendly experiences. Available for U.S. remote roles\n        with 9 AM EST - 6 PM IST overlap.\n      </p>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AACA;AAAA;;;;AAEO,MAAM,OAAiB;IAC5B,qBACE,8OAAC;;0BACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAAqI;;;;;;;;;;;kCAMtJ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;0CAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,MAAM;;;;;;;;;;;0CAGhB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;0CAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,MAAM;;;;;;;;;;;0CAGlB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;;;;;;0CAGd,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAMnB,8OAAC;gBAAG,WAAU;0BAAuD;;;;;;0BAIrE,8OAAC;gBAAE,WAAU;0BAA+B;;;;;;0BAI5C,8OAAC;gBAAE,WAAU;0BAAyB;;;;;;;;;;;;AAQ5C", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/Experience.tsx"], "sourcesContent": ["import React from 'react';\nimport { Building2, Briefcase } from 'lucide-react';\nimport { <PERSON>, Card<PERSON>eader, CardContent, CardTitle, CardDescription, Badge, BadgeGroup, Timeline, TimelineItem } from './ui';\n\nconst experiences = [\n  {\n    id: 1,\n    title: \"Lead Developer (Full Stack & Cloud Infrastructure)\",\n    company: \"Undisclosed Client (Electoral Platform)\",\n    duration: \"Nov 2024 - Feb 2025\",\n    location: \"Mumbai, India\",\n    status: \"Current\",\n    description: \"Pioneered India's first fully streamlined electoral-sampling platform\",\n    achievements: [\n      \"Led end-to-end R&D and full-stack implementation to eliminate manual data collection\",\n      \"Built distributed web scraping system on AWS EC2 with S3 for data storage\",\n      \"Reduced data collection time by 9600 man-days worth of work\",\n      \"Engineered high-throughput data extraction achieving 50x improvement (4000 per hour)\",\n      \"Built advanced image processing modules with 97% accuracy in data extraction\"\n    ],\n    technologies: [\"Python\", \"AWS EC2\", \"AWS S3\", \"Selenium\", \"Tesseract\", \"EasyOCR\", \"Multi-threading\"],\n    links: [\n      { label: \"Platform\", url: \"https://edownloaders.com\", icon: \"globe\" }\n    ],\n    icon: Building2,\n    color: \"blue\"\n  },\n  {\n    id: 2,\n    title: \"Software Intern (Business Development Automation)\",\n    company: \"Viven Ediversity Pvt. Ltd.\",\n    duration: \"June 2024 - Jan 2025\",\n    location: \"Thane, India\",\n    status: \"Past\",\n    description: \"Executed comprehensive website testing and business automation solutions\",\n    achievements: [\n      \"Launched 60 courses with 100% positive response rate\",\n      \"Integrated WhatsApp Business API saving 3+ hours daily\",\n      \"Automated 500+ warm emails daily through custom solutions\",\n      \"Implemented SEO best practices and endpoint integrations\"\n    ],\n    technologies: [\"Google Sheets Apps Script\", \"Pabbly\", \"WhatsApp Business API\", \"SEO\"],\n    links: [],\n    icon: Briefcase,\n    color: \"green\"\n  }\n];\n\nexport const Experience: React.FC = () => {\n  return (\n    <section className=\"py-20\">\n      <h2 className=\"text-2xl font-medium mb-3 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text\">\n        Experience\n      </h2>\n      <p className=\"text-gray-400 mb-8\">\n        Here's a timeline of my professional journey, showcasing my roles and contributions in \n        full-stack development, cloud infrastructure, and business automation.\n      </p>\n      \n      <Timeline>\n        {experiences.map((exp) => (\n          <TimelineItem key={exp.id}>\n            <Card gradient hover>\n              <CardHeader>\n                <div className=\"flex flex-col sm:flex-row items-start gap-4\">\n                  <div className=\"p-2 rounded-lg bg-gray-800\">\n                    <exp.icon className={`w-5 h-5 ${exp.color === 'blue' ? 'text-blue-400' : 'text-green-400'}`} />\n                  </div>\n                  \n                  <div className=\"space-y-1.5 flex-grow w-full\">\n                    <div className=\"flex flex-col sm:flex-row sm:items-center justify-between gap-2\">\n                      <div className=\"flex flex-wrap items-center gap-2\">\n                        <CardTitle>{exp.title}</CardTitle>\n                        <span className=\"text-sm text-[var(--text-muted)]\">•</span>\n                        <span className=\"text-[var(--text-muted)]\">{exp.company}</span>\n                      </div>\n                      \n                      <div className=\"flex flex-wrap gap-2\">\n                        {exp.links.map((link, index) => (\n                          <Badge \n                            key={index}\n                            href={link.url}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            variant=\"outline\"\n                          >\n                            {link.label}\n                          </Badge>\n                        ))}\n                      </div>\n                    </div>\n                    \n                    <div className=\"text-sm text-gray-400\">{exp.duration}</div>\n                    <div className=\"text-sm text-gray-400\">{exp.location}</div>\n                    <CardDescription>{exp.description}</CardDescription>\n                    \n                    {/* Achievements */}\n                    <div className=\"mt-3\">\n                      <h4 className=\"text-sm font-medium text-white mb-2\">Key Achievements:</h4>\n                      <ul className=\"text-sm text-gray-400 space-y-1\">\n                        {exp.achievements.map((achievement, index) => (\n                          <li key={index} className=\"flex items-start gap-2\">\n                            <span className=\"text-blue-400 mt-1\">•</span>\n                            <span>{achievement}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  </div>\n                </div>\n              </CardHeader>\n              \n              <CardContent>\n                <BadgeGroup>\n                  {exp.technologies.map((tech, index) => (\n                    <Badge key={index}>{tech}</Badge>\n                  ))}\n                </BadgeGroup>\n              </CardContent>\n            </Card>\n          </TimelineItem>\n        ))}\n      </Timeline>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;AAAA;AACA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAU;YAAW;YAAU;YAAY;YAAa;YAAW;SAAkB;QACpG,OAAO;YACL;gBAAE,OAAO;gBAAY,KAAK;gBAA4B,MAAM;YAAQ;SACrE;QACD,MAAM,gNAAA,CAAA,YAAS;QACf,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAA6B;YAAU;YAAyB;SAAM;QACrF,OAAO,EAAE;QACT,MAAM,4MAAA,CAAA,YAAS;QACf,OAAO;IACT;CACD;AAEM,MAAM,aAAuB;IAClC,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAG,WAAU;0BAAqG;;;;;;0BAGnH,8OAAC;gBAAE,WAAU;0BAAqB;;;;;;0BAKlC,8OAAC,oIAAA,CAAA,WAAQ;0BACN,YAAY,GAAG,CAAC,CAAC,oBAChB,8OAAC,oIAAA,CAAA,eAAY;kCACX,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,QAAQ;4BAAC,KAAK;;8CAClB,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,IAAI,IAAI;oDAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,KAAK,KAAK,SAAS,kBAAkB,kBAAkB;;;;;;;;;;;0DAG7F,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gIAAA,CAAA,YAAS;kFAAE,IAAI,KAAK;;;;;;kFACrB,8OAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,8OAAC;wEAAK,WAAU;kFAA4B,IAAI,OAAO;;;;;;;;;;;;0EAGzD,8OAAC;gEAAI,WAAU;0EACZ,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,iIAAA,CAAA,QAAK;wEAEJ,MAAM,KAAK,GAAG;wEACd,QAAO;wEACP,KAAI;wEACJ,SAAQ;kFAEP,KAAK,KAAK;uEANN;;;;;;;;;;;;;;;;kEAYb,8OAAC;wDAAI,WAAU;kEAAyB,IAAI,QAAQ;;;;;;kEACpD,8OAAC;wDAAI,WAAU;kEAAyB,IAAI,QAAQ;;;;;;kEACpD,8OAAC,gIAAA,CAAA,kBAAe;kEAAE,IAAI,WAAW;;;;;;kEAGjC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAsC;;;;;;0EACpD,8OAAC;gEAAG,WAAU;0EACX,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBAClC,8OAAC;wEAAe,WAAU;;0FACxB,8OAAC;gFAAK,WAAU;0FAAqB;;;;;;0FACrC,8OAAC;0FAAM;;;;;;;uEAFA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAWrB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,iIAAA,CAAA,aAAU;kDACR,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC3B,8OAAC,iIAAA,CAAA,QAAK;0DAAc;+CAAR;;;;;;;;;;;;;;;;;;;;;uBAtDH,IAAI,EAAE;;;;;;;;;;;;;;;;AAgEnC", "debugId": null}}, {"offset": {"line": 969, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/Projects.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Projects = registerClientReference(\n    function() { throw new Error(\"Attempted to call Projects() from the server but Projects is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Projects.tsx <module evaluation>\",\n    \"Projects\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,6DACA", "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/Projects.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Projects = registerClientReference(\n    function() { throw new Error(\"Attempted to call Projects() from the server but Projects is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Projects.tsx\",\n    \"Projects\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,yCACA", "debugId": null}}, {"offset": {"line": 997, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1007, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/Skills.tsx"], "sourcesContent": ["import React from 'react';\nimport { Code, Database, Cloud, Brain, <PERSON>ch, GitBranch } from 'lucide-react';\nimport { <PERSON>, <PERSON>H<PERSON>er, CardContent, CardTitle, Badge, BadgeGroup } from './ui';\n\nconst skillCategories = [\n  {\n    id: 1,\n    title: \"Programming Languages\",\n    icon: Code,\n    color: \"blue\",\n    skills: [\n      { name: \"Python\", level: \"Primary\", description: \"Multiple projects, extensive experience\" },\n      { name: \"Java\", level: \"Intermediate\", description: \"Academic and project experience\" },\n      { name: \"C++\", level: \"Basic\", description: \"Some knowledge, academic background\" },\n      { name: \"JavaScript/TypeScript\", level: \"Intermediate\", description: \"Frontend and backend development\" }\n    ]\n  },\n  {\n    id: 2,\n    title: \"Frameworks & Technologies\",\n    icon: Wrench,\n    color: \"green\",\n    skills: [\n      { name: \"Django\", level: \"Advanced\", description: \"Backend web development\" },\n      { name: \"React.js\", level: \"Advanced\", description: \"Frontend development\" },\n      { name: \"Next.js\", level: \"Advanced\", description: \"Full-stack React framework\" },\n      { name: \"Node.js\", level: \"Intermediate\", description: \"Backend JavaScript runtime\" },\n      { name: \"Angular\", level: \"Intermediate\", description: \"Frontend framework\" },\n      { name: \"Tailwind CSS\", level: \"Advanced\", description: \"Utility-first CSS framework\" }\n    ]\n  },\n  {\n    id: 3,\n    title: \"Machine Learning & AI\",\n    icon: Brain,\n    color: \"purple\",\n    skills: [\n      { name: \"TensorFlow\", level: \"Intermediate\", description: \"Deep learning framework\" },\n      { name: \"PyTorch\", level: \"Intermediate\", description: \"Machine learning library\" },\n      { name: \"Scikit-learn\", level: \"Advanced\", description: \"Machine learning algorithms\" },\n      { name: \"Transformers\", level: \"Intermediate\", description: \"NLP models\" },\n      { name: \"CNNs & RNNs\", level: \"Intermediate\", description: \"Neural network architectures\" },\n      { name: \"OpenAI API\", level: \"Advanced\", description: \"GPT-4, GPT-3.5, Codex integration\" },\n      { name: \"Hugging Face\", level: \"Intermediate\", description: \"Model hub and transformers\" }\n    ]\n  },\n  {\n    id: 4,\n    title: \"Cloud & DevOps\",\n    icon: Cloud,\n    color: \"cyan\",\n    skills: [\n      { name: \"AWS\", level: \"Advanced\", description: \"EC2, S3, Lambda services\" },\n      { name: \"Docker\", level: \"Intermediate\", description: \"Containerization\" },\n      { name: \"Kubernetes\", level: \"Basic\", description: \"Container orchestration\" },\n      { name: \"CI/CD\", level: \"Intermediate\", description: \"Jenkins, GitHub Actions\" },\n      { name: \"Git/GitHub\", level: \"Advanced\", description: \"Version control\" }\n    ]\n  },\n  {\n    id: 5,\n    title: \"Databases & Tools\",\n    icon: Database,\n    color: \"orange\",\n    skills: [\n      { name: \"MongoDB\", level: \"Advanced\", description: \"NoSQL database\" },\n      { name: \"MySQL\", level: \"Intermediate\", description: \"Relational database\" },\n      { name: \"SQLite3\", level: \"Intermediate\", description: \"Lightweight database\" },\n      { name: \"Selenium\", level: \"Advanced\", description: \"Web scraping and automation\" },\n      { name: \"Tesseract & EasyOCR\", level: \"Advanced\", description: \"Image processing and OCR\" }\n    ]\n  },\n  {\n    id: 6,\n    title: \"Additional Tools\",\n    icon: GitBranch,\n    color: \"pink\",\n    skills: [\n      { name: \"RESTful APIs\", level: \"Advanced\", description: \"API design and integration\" },\n      { name: \"Socket.io\", level: \"Intermediate\", description: \"Real-time communication\" },\n      { name: \"Multi-threading\", level: \"Advanced\", description: \"Parallel processing\" },\n      { name: \"Aider CLI\", level: \"Intermediate\", description: \"AI-assisted coding\" },\n      { name: \"VS Code\", level: \"Advanced\", description: \"Development environment\" }\n    ]\n  }\n];\n\nconst getColorClass = (color: string) => {\n  const colorMap: { [key: string]: string } = {\n    blue: 'text-blue-400',\n    green: 'text-green-400',\n    purple: 'text-purple-400',\n    cyan: 'text-cyan-400',\n    orange: 'text-orange-400',\n    pink: 'text-pink-400'\n  };\n  return colorMap[color] || 'text-blue-400';\n};\n\nexport const Skills: React.FC = () => {\n  return (\n    <section className=\"py-20\">\n      <h2 className=\"text-2xl font-medium mb-3 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text\">\n        Skills & Technologies\n      </h2>\n      <p className=\"text-gray-400 mb-8\">\n        A comprehensive overview of my technical skills across different domains, \n        from programming languages to cloud technologies and AI/ML frameworks.\n      </p>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        {skillCategories.map((category) => (\n          <Card key={category.id} hover className=\"h-fit\">\n            <CardHeader>\n              <div className=\"flex items-center gap-3 mb-4\">\n                <div className=\"p-2 rounded-lg bg-gray-800\">\n                  <category.icon className={`w-5 h-5 ${getColorClass(category.color)}`} />\n                </div>\n                <CardTitle className=\"text-lg\">{category.title}</CardTitle>\n              </div>\n            </CardHeader>\n            \n            <CardContent>\n              <div className=\"space-y-4\">\n                {category.skills.map((skill, index) => (\n                  <div key={index} className=\"flex flex-col gap-2\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"font-medium text-white\">{skill.name}</span>\n                      <Badge variant=\"secondary\" className=\"text-xs\">\n                        {skill.level}\n                      </Badge>\n                    </div>\n                    <p className=\"text-sm text-gray-400\">{skill.description}</p>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;;;;AAEA,MAAM,kBAAkB;IACtB;QACE,IAAI;QACJ,OAAO;QACP,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAU,OAAO;gBAAW,aAAa;YAA0C;YAC3F;gBAAE,MAAM;gBAAQ,OAAO;gBAAgB,aAAa;YAAkC;YACtF;gBAAE,MAAM;gBAAO,OAAO;gBAAS,aAAa;YAAsC;YAClF;gBAAE,MAAM;gBAAyB,OAAO;gBAAgB,aAAa;YAAmC;SACzG;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAU,OAAO;gBAAY,aAAa;YAA0B;YAC5E;gBAAE,MAAM;gBAAY,OAAO;gBAAY,aAAa;YAAuB;YAC3E;gBAAE,MAAM;gBAAW,OAAO;gBAAY,aAAa;YAA6B;YAChF;gBAAE,MAAM;gBAAW,OAAO;gBAAgB,aAAa;YAA6B;YACpF;gBAAE,MAAM;gBAAW,OAAO;gBAAgB,aAAa;YAAqB;YAC5E;gBAAE,MAAM;gBAAgB,OAAO;gBAAY,aAAa;YAA8B;SACvF;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAc,OAAO;gBAAgB,aAAa;YAA0B;YACpF;gBAAE,MAAM;gBAAW,OAAO;gBAAgB,aAAa;YAA2B;YAClF;gBAAE,MAAM;gBAAgB,OAAO;gBAAY,aAAa;YAA8B;YACtF;gBAAE,MAAM;gBAAgB,OAAO;gBAAgB,aAAa;YAAa;YACzE;gBAAE,MAAM;gBAAe,OAAO;gBAAgB,aAAa;YAA+B;YAC1F;gBAAE,MAAM;gBAAc,OAAO;gBAAY,aAAa;YAAoC;YAC1F;gBAAE,MAAM;gBAAgB,OAAO;gBAAgB,aAAa;YAA6B;SAC1F;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAO,OAAO;gBAAY,aAAa;YAA2B;YAC1E;gBAAE,MAAM;gBAAU,OAAO;gBAAgB,aAAa;YAAmB;YACzE;gBAAE,MAAM;gBAAc,OAAO;gBAAS,aAAa;YAA0B;YAC7E;gBAAE,MAAM;gBAAS,OAAO;gBAAgB,aAAa;YAA0B;YAC/E;gBAAE,MAAM;gBAAc,OAAO;gBAAY,aAAa;YAAkB;SACzE;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAW,OAAO;gBAAY,aAAa;YAAiB;YACpE;gBAAE,MAAM;gBAAS,OAAO;gBAAgB,aAAa;YAAsB;YAC3E;gBAAE,MAAM;gBAAW,OAAO;gBAAgB,aAAa;YAAuB;YAC9E;gBAAE,MAAM;gBAAY,OAAO;gBAAY,aAAa;YAA8B;YAClF;gBAAE,MAAM;gBAAuB,OAAO;gBAAY,aAAa;YAA2B;SAC3F;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,gNAAA,CAAA,YAAS;QACf,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAgB,OAAO;gBAAY,aAAa;YAA6B;YACrF;gBAAE,MAAM;gBAAa,OAAO;gBAAgB,aAAa;YAA0B;YACnF;gBAAE,MAAM;gBAAmB,OAAO;gBAAY,aAAa;YAAsB;YACjF;gBAAE,MAAM;gBAAa,OAAO;gBAAgB,aAAa;YAAqB;YAC9E;gBAAE,MAAM;gBAAW,OAAO;gBAAY,aAAa;YAA0B;SAC9E;IACH;CACD;AAED,MAAM,gBAAgB,CAAC;IACrB,MAAM,WAAsC;QAC1C,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,MAAM;IACR;IACA,OAAO,QAAQ,CAAC,MAAM,IAAI;AAC5B;AAEO,MAAM,SAAmB;IAC9B,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAG,WAAU;0BAAqG;;;;;;0BAGnH,8OAAC;gBAAE,WAAU;0BAAqB;;;;;;0BAKlC,8OAAC;gBAAI,WAAU;0BACZ,gBAAgB,GAAG,CAAC,CAAC,yBACpB,8OAAC,gIAAA,CAAA,OAAI;wBAAmB,KAAK;wBAAC,WAAU;;0CACtC,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,SAAS,IAAI;gDAAC,WAAW,CAAC,QAAQ,EAAE,cAAc,SAAS,KAAK,GAAG;;;;;;;;;;;sDAEtE,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAW,SAAS,KAAK;;;;;;;;;;;;;;;;;0CAIlD,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA0B,MAAM,IAAI;;;;;;sEACpD,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAClC,MAAM,KAAK;;;;;;;;;;;;8DAGhB,8OAAC;oDAAE,WAAU;8DAAyB,MAAM,WAAW;;;;;;;2CAP/C;;;;;;;;;;;;;;;;uBAbP,SAAS,EAAE;;;;;;;;;;;;;;;;AA8BhC", "debugId": null}}, {"offset": {"line": 1387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/Education.tsx"], "sourcesContent": ["import React from 'react';\nimport { GraduationCap, Award, BookOpen, Trophy } from 'lucide-react';\nimport { <PERSON>, <PERSON>H<PERSON>er, CardContent, CardTitle, CardDescription, Badge, BadgeGroup } from './ui';\n\nexport const Education: React.FC = () => {\n  return (\n    <section className=\"py-20\">\n      <h2 className=\"text-2xl font-medium mb-3 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text\">\n        Education & Achievements\n      </h2>\n      <p className=\"text-[var(--text-muted)] mb-8\">\n        My academic background, research contributions, and notable achievements \n        in competitive examinations and hackathons.\n      </p>\n      \n      <div className=\"space-y-6\">\n        {/* Main Education */}\n        <Card gradient hover>\n          <CardHeader>\n            <div className=\"flex flex-col sm:flex-row items-start gap-4\">\n              <div className=\"p-2 rounded-lg bg-[var(--hover-background)]\">\n                <GraduationCap className=\"w-5 h-5 text-blue-400\" />\n              </div>\n              \n              <div className=\"space-y-1.5 flex-grow w-full\">\n                <div className=\"flex flex-col sm:flex-row sm:items-center justify-between gap-2\">\n                  <div className=\"flex flex-wrap items-center gap-2\">\n                    <CardTitle>Bachelor of Technology - Computer Science and Engineering</CardTitle>\n                  </div>\n                  <Badge variant=\"status\">Graduated</Badge>\n                </div>\n                \n                <div className=\"text-sm text-[var(--text-muted)]\">\n                  Indian Institute of Information Technology, Pune\n                </div>\n                <div className=\"text-sm text-[var(--text-muted)]\">\n                  CGPA: 8.14/10\n                </div>\n                <CardDescription>\n                  Comprehensive computer science education with focus on software engineering, \n                  algorithms, and emerging technologies.\n                </CardDescription>\n                \n                {/* Key Courses */}\n                <div className=\"mt-4\">\n                  <h4 className=\"text-sm font-medium text-[var(--text-primary)] mb-2\">Key Courses:</h4>\n                  <BadgeGroup>\n                    {[\n                      \"C++\", \"Java\", \"Python\", \"Statistics\", \"Data Structures & Algorithms\",\n                      \"Database Management (SQL)\", \"OOP\", \"Machine Learning\", \"Cloud Computing\",\n                      \"Big Data\", \"High Performance & Distributed Computing\"\n                    ].map((course, index) => (\n                      <Badge key={index}>{course}</Badge>\n                    ))}\n                  </BadgeGroup>\n                </div>\n              </div>\n            </div>\n          </CardHeader>\n        </Card>\n\n        {/* Research */}\n        <Card hover>\n          <CardHeader>\n            <div className=\"flex flex-col sm:flex-row items-start gap-4\">\n              <div className=\"p-2 rounded-lg bg-[var(--hover-background)]\">\n                <BookOpen className=\"w-5 h-5 text-green-400\" />\n              </div>\n              \n              <div className=\"space-y-1.5 flex-grow w-full\">\n                <CardTitle>Academic Research Paper</CardTitle>\n                <CardDescription>\n                  Deep Learning Model resulting in a 28 dB average PSNR improvement & 36% OCR improvement\n                </CardDescription>\n                \n                <div className=\"mt-3\">\n                  <h4 className=\"text-sm font-medium text-[var(--text-primary)] mb-2\">Research Impact:</h4>\n                  <ul className=\"text-sm text-[var(--text-muted)] space-y-1\">\n                    <li className=\"flex items-start gap-2\">\n                      <span className=\"text-green-400 mt-1\">•</span>\n                      <span>28 dB average PSNR (Peak Signal-to-Noise Ratio) improvement</span>\n                    </li>\n                    <li className=\"flex items-start gap-2\">\n                      <span className=\"text-green-400 mt-1\">•</span>\n                      <span>36% improvement in OCR (Optical Character Recognition) accuracy</span>\n                    </li>\n                    <li className=\"flex items-start gap-2\">\n                      <span className=\"text-green-400 mt-1\">•</span>\n                      <span>Novel deep learning approach for image processing enhancement</span>\n                    </li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </CardHeader>\n        </Card>\n\n        {/* Achievements */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <Card hover>\n            <CardHeader>\n              <div className=\"flex items-start gap-4\">\n                <div className=\"p-2 rounded-lg bg-[var(--hover-background)]\">\n                  <Trophy className=\"w-5 h-5 text-yellow-400\" />\n                </div>\n                \n                <div className=\"space-y-1.5\">\n                  <CardTitle className=\"text-base\">Hackathon Achievement</CardTitle>\n                  <CardDescription>\n                    4th place in western region - Solving For India Hackathon among 2000+ teams\n                  </CardDescription>\n                  \n                  <div className=\"mt-3\">\n                    <ul className=\"text-sm text-[var(--text-muted)] space-y-1\">\n                      <li className=\"flex items-start gap-2\">\n                        <span className=\"text-yellow-400 mt-1\">•</span>\n                        <span>Blockchain health-record NFTs project</span>\n                      </li>\n                      <li className=\"flex items-start gap-2\">\n                        <span className=\"text-yellow-400 mt-1\">•</span>\n                        <span>Selected by Google for experience sharing</span>\n                      </li>\n                      <li className=\"flex items-start gap-2\">\n                        <span className=\"text-yellow-400 mt-1\">•</span>\n                        <span>Led team development and presentation</span>\n                      </li>\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            </CardHeader>\n          </Card>\n\n          <Card hover>\n            <CardHeader>\n              <div className=\"flex items-start gap-4\">\n                <div className=\"p-2 rounded-lg bg-[var(--hover-background)]\">\n                  <Award className=\"w-5 h-5 text-purple-400\" />\n                </div>\n                \n                <div className=\"space-y-1.5\">\n                  <CardTitle className=\"text-base\">Competitive Achievements</CardTitle>\n                  <CardDescription>\n                    Outstanding performance in national level competitive examinations\n                  </CardDescription>\n                  \n                  <div className=\"mt-3\">\n                    <ul className=\"text-sm text-[var(--text-muted)] space-y-1\">\n                      <li className=\"flex items-start gap-2\">\n                        <span className=\"text-purple-400 mt-1\">•</span>\n                        <span>JEE 2021: 97.9 percentile overall</span>\n                      </li>\n                      <li className=\"flex items-start gap-2\">\n                        <span className=\"text-purple-400 mt-1\">•</span>\n                        <span>JEE 2021 Math: 99.5 percentile</span>\n                      </li>\n                      <li className=\"flex items-start gap-2\">\n                        <span className=\"text-purple-400 mt-1\">•</span>\n                        <span>Intermediate Degree: 87.7% (Bi-Focal Science)</span>\n                      </li>\n                      <li className=\"flex items-start gap-2\">\n                        <span className=\"text-purple-400 mt-1\">•</span>\n                        <span>Class Representative - Batch of '25</span>\n                      </li>\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            </CardHeader>\n          </Card>\n        </div>\n      </div>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;;;;AAEO,MAAM,YAAsB;IACjC,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAG,WAAU;0BAAqG;;;;;;0BAGnH,8OAAC;gBAAE,WAAU;0BAAgC;;;;;;0BAK7C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,QAAQ;wBAAC,KAAK;kCAClB,cAAA,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;kDAG3B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,gIAAA,CAAA,YAAS;sEAAC;;;;;;;;;;;kEAEb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAS;;;;;;;;;;;;0DAG1B,8OAAC;gDAAI,WAAU;0DAAmC;;;;;;0DAGlD,8OAAC;gDAAI,WAAU;0DAAmC;;;;;;0DAGlD,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;0DAMjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAsD;;;;;;kEACpE,8OAAC,iIAAA,CAAA,aAAU;kEACR;4DACC;4DAAO;4DAAQ;4DAAU;4DAAc;4DACvC;4DAA6B;4DAAO;4DAAoB;4DACxD;4DAAY;yDACb,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACb,8OAAC,iIAAA,CAAA,QAAK;0EAAc;+DAAR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU1B,8OAAC,gIAAA,CAAA,OAAI;wBAAC,KAAK;kCACT,cAAA,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAGtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;0DAIjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAsD;;;;;;kEACpE,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;wEAAK,WAAU;kFAAsB;;;;;;kFACtC,8OAAC;kFAAK;;;;;;;;;;;;0EAER,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;wEAAK,WAAU;kFAAsB;;;;;;kFACtC,8OAAC;kFAAK;;;;;;;;;;;;0EAER,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;wEAAK,WAAU;kFAAsB;;;;;;kFACtC,8OAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,KAAK;0CACT,cAAA,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAGpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAY;;;;;;kEACjC,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;kEAIjB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAK,WAAU;sFAAuB;;;;;;sFACvC,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAK,WAAU;sFAAuB;;;;;;sFACvC,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAK,WAAU;sFAAuB;;;;;;sFACvC,8OAAC;sFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASpB,8OAAC,gIAAA,CAAA,OAAI;gCAAC,KAAK;0CACT,cAAA,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAGnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAY;;;;;;kEACjC,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;kEAIjB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAK,WAAU;sFAAuB;;;;;;sFACvC,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAK,WAAU;sFAAuB;;;;;;sFACvC,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAK,WAAU;sFAAuB;;;;;;sFACvC,8OAAC;sFAAK;;;;;;;;;;;;8EAER,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAK,WAAU;sFAAuB;;;;;;sFACvC,8OAAC;sFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY9B", "debugId": null}}, {"offset": {"line": 2071, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/Leadership.tsx"], "sourcesContent": ["import React from 'react';\nimport { Users, Megaphone, Trophy, Star } from 'lucide-react';\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardTitle, CardDescription, Badge } from './ui';\n\nconst leadershipActivities = [\n  {\n    id: 1,\n    title: \"E-Cell, IIIT Pune - Marketing Associate\",\n    icon: Megaphone,\n    color: \"blue\",\n    description: \"Led marketing initiatives and vendor communications for major college events\",\n    achievements: [\n      \"Pitched sponsorships for E-Summit 2k23 with 20,000+ participants\",\n      \"Managed vendor communications and negotiations\",\n      \"Contributed to E-Summit 2k22 and Ideathon 2k22 events\",\n      \"Implemented automated payment verification systems\",\n      \"Coordinated project management and event logistics\"\n    ],\n    impact: \"Successfully secured sponsorships and managed large-scale event operations\"\n  },\n  {\n    id: 2,\n    title: \"Hackathon Leadership\",\n    icon: Trophy,\n    color: \"yellow\",\n    description: \"Led development teams in competitive programming and innovation challenges\",\n    achievements: [\n      \"Led team to 4th place in western region (Solving For India Hackathon)\",\n      \"Competed among 2000+ teams nationwide\",\n      \"Developed Blockchain health-record NFTs solution\",\n      \"Selected by Google for experience sharing session\",\n      \"Demonstrated technical leadership and project management\"\n    ],\n    impact: \"Showcased technical innovation and team leadership capabilities\"\n  },\n  {\n    id: 3,\n    title: \"Class Representative - Batch of '25\",\n    icon: Users,\n    color: \"green\",\n    description: \"Organized and facilitated campus initiatives as student representative\",\n    achievements: [\n      \"Represented student interests in academic committees\",\n      \"Organized campus initiatives and student activities\",\n      \"Facilitated communication between students and faculty\",\n      \"Coordinated batch events and academic discussions\",\n      \"Maintained high academic standards (8.14/10 CGPA)\"\n    ],\n    impact: \"Enhanced student experience and academic environment\"\n  },\n  {\n    id: 4,\n    title: \"Technical Innovation & Mentorship\",\n    icon: Star,\n    color: \"purple\",\n    description: \"Contributed to technical community through innovation and knowledge sharing\",\n    achievements: [\n      \"Published academic research with significant improvements\",\n      \"Mentored junior students in programming and development\",\n      \"Contributed to open-source projects and technical communities\",\n      \"Implemented AI-assisted development workflows\",\n      \"Shared expertise in web scraping and automation\"\n    ],\n    impact: \"Advanced technical knowledge and community contribution\"\n  }\n];\n\nexport const Leadership: React.FC = () => {\n  return (\n    <section className=\"py-20\">\n      <h2 className=\"text-2xl font-medium mb-3 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text\">\n        Leadership & Activities\n      </h2>\n      <p className=\"text-[var(--text-muted)] mb-8\">\n        My leadership experience, community involvement, and contributions to \n        student organizations, hackathons, and technical initiatives.\n      </p>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        {leadershipActivities.map((activity) => (\n          <Card key={activity.id} hover className=\"h-fit\">\n            <CardHeader>\n              <div className=\"flex items-start gap-4\">\n                <div className=\"p-2 rounded-lg bg-[var(--hover-background)]\">\n                  <activity.icon className={`w-5 h-5 text-${activity.color}-400`} />\n                </div>\n                \n                <div className=\"space-y-1.5 flex-grow\">\n                  <CardTitle className=\"text-lg\">{activity.title}</CardTitle>\n                  <CardDescription>{activity.description}</CardDescription>\n                  \n                  {/* Impact Badge */}\n                  <Badge variant=\"secondary\" className=\"mt-2\">\n                    {activity.impact}\n                  </Badge>\n                </div>\n              </div>\n            </CardHeader>\n            \n            <CardContent>\n              <div className=\"space-y-3\">\n                <h4 className=\"text-sm font-medium text-[var(--text-primary)]\">Key Contributions:</h4>\n                <ul className=\"space-y-2\">\n                  {activity.achievements.map((achievement, index) => (\n                    <li key={index} className=\"flex items-start gap-2 text-sm text-[var(--text-muted)]\">\n                      <span className={`text-${activity.color}-400 mt-1`}>•</span>\n                      <span>{achievement}</span>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n      \n      {/* Summary Stats */}\n      <div className=\"mt-12 grid grid-cols-2 md:grid-cols-4 gap-4\">\n        <div className=\"text-center p-4 rounded-lg bg-[var(--card-background)] border border-[var(--card-border)]\">\n          <div className=\"text-2xl font-bold text-blue-400\">20,000+</div>\n          <div className=\"text-sm text-[var(--text-muted)]\">Event Participants</div>\n        </div>\n        <div className=\"text-center p-4 rounded-lg bg-[var(--card-background)] border border-[var(--card-border)]\">\n          <div className=\"text-2xl font-bold text-yellow-400\">4th</div>\n          <div className=\"text-sm text-[var(--text-muted)]\">Hackathon Rank</div>\n        </div>\n        <div className=\"text-center p-4 rounded-lg bg-[var(--card-background)] border border-[var(--card-border)]\">\n          <div className=\"text-2xl font-bold text-green-400\">2000+</div>\n          <div className=\"text-sm text-[var(--text-muted)]\">Competing Teams</div>\n        </div>\n        <div className=\"text-center p-4 rounded-lg bg-[var(--card-background)] border border-[var(--card-border)]\">\n          <div className=\"text-2xl font-bold text-purple-400\">8.14</div>\n          <div className=\"text-sm text-[var(--text-muted)]\">CGPA/10</div>\n        </div>\n      </div>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;;;;AAEA,MAAM,uBAAuB;IAC3B;QACE,IAAI;QACJ,OAAO;QACP,MAAM,4MAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,QAAQ;IACV;CACD;AAEM,MAAM,aAAuB;IAClC,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAG,WAAU;0BAAqG;;;;;;0BAGnH,8OAAC;gBAAE,WAAU;0BAAgC;;;;;;0BAK7C,8OAAC;gBAAI,WAAU;0BACZ,qBAAqB,GAAG,CAAC,CAAC,yBACzB,8OAAC,gIAAA,CAAA,OAAI;wBAAmB,KAAK;wBAAC,WAAU;;0CACtC,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,SAAS,IAAI;gDAAC,WAAW,CAAC,aAAa,EAAE,SAAS,KAAK,CAAC,IAAI,CAAC;;;;;;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAW,SAAS,KAAK;;;;;;8DAC9C,8OAAC,gIAAA,CAAA,kBAAe;8DAAE,SAAS,WAAW;;;;;;8DAGtC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAClC,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAMxB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAiD;;;;;;sDAC/D,8OAAC;4CAAG,WAAU;sDACX,SAAS,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBACvC,8OAAC;oDAAe,WAAU;;sEACxB,8OAAC;4DAAK,WAAW,CAAC,KAAK,EAAE,SAAS,KAAK,CAAC,SAAS,CAAC;sEAAE;;;;;;sEACpD,8OAAC;sEAAM;;;;;;;mDAFA;;;;;;;;;;;;;;;;;;;;;;uBAxBR,SAAS,EAAE;;;;;;;;;;0BAqC1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAmC;;;;;;0CAClD,8OAAC;gCAAI,WAAU;0CAAmC;;;;;;;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAqC;;;;;;0CACpD,8OAAC;gCAAI,WAAU;0CAAmC;;;;;;;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAoC;;;;;;0CACnD,8OAAC;gCAAI,WAAU;0CAAmC;;;;;;;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAqC;;;;;;0CACpD,8OAAC;gCAAI,WAAU;0CAAmC;;;;;;;;;;;;;;;;;;;;;;;;AAK5D", "debugId": null}}, {"offset": {"line": 2422, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/components/Contact.tsx"], "sourcesContent": ["import React from 'react';\nimport { Mail, Phone, MapPin, <PERSON><PERSON><PERSON>, <PERSON>edin, Clock, Globe } from 'lucide-react';\nimport { <PERSON>, CardHeader, CardContent, CardTitle, CardDescription, Button } from './ui';\n\nexport const Contact: React.FC = () => {\n  return (\n    <section className=\"py-20\">\n      <h2 className=\"text-2xl font-medium mb-3 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text\">\n        Get In Touch\n      </h2>\n      <p className=\"text-[var(--text-muted)] mb-8\">\n        I'm available for U.S. remote roles with excellent timezone overlap. \n        Let's discuss how I can contribute to your team's success.\n      </p>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        {/* Contact Information */}\n        <Card hover>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Mail className=\"w-5 h-5 text-blue-400\" />\n              Contact Information\n            </CardTitle>\n            <CardDescription>\n              Ready to collaborate on exciting projects and opportunities\n            </CardDescription>\n          </CardHeader>\n          \n          <CardContent>\n            <div className=\"space-y-4\">\n              {/* Email */}\n              <div className=\"flex items-center gap-3\">\n                <div className=\"p-2 rounded-lg bg-[var(--hover-background)]\">\n                  <Mail className=\"w-4 h-4 text-blue-400\" />\n                </div>\n                <div>\n                  <div className=\"font-medium text-[var(--text-primary)]\">Email</div>\n                  <a \n                    href=\"mailto:<EMAIL>\" \n                    className=\"text-sm text-[var(--text-secondary)] hover:text-[var(--text-primary)] transition-colors\"\n                  >\n                    <EMAIL>\n                  </a>\n                </div>\n              </div>\n              \n              {/* Phone */}\n              <div className=\"flex items-center gap-3\">\n                <div className=\"p-2 rounded-lg bg-[var(--hover-background)]\">\n                  <Phone className=\"w-4 h-4 text-green-400\" />\n                </div>\n                <div>\n                  <div className=\"font-medium text-[var(--text-primary)]\">Phone</div>\n                  <a \n                    href=\"tel:+919356252711\" \n                    className=\"text-sm text-[var(--text-secondary)] hover:text-[var(--text-primary)] transition-colors\"\n                  >\n                    (+91) 9356252711\n                  </a>\n                </div>\n              </div>\n              \n              {/* Location */}\n              <div className=\"flex items-center gap-3\">\n                <div className=\"p-2 rounded-lg bg-[var(--hover-background)]\">\n                  <MapPin className=\"w-4 h-4 text-purple-400\" />\n                </div>\n                <div>\n                  <div className=\"font-medium text-[var(--text-primary)]\">Location</div>\n                  <div className=\"text-sm text-[var(--text-secondary)]\">Mumbai, India</div>\n                </div>\n              </div>\n              \n              {/* Timezone */}\n              <div className=\"flex items-center gap-3\">\n                <div className=\"p-2 rounded-lg bg-[var(--hover-background)]\">\n                  <Clock className=\"w-4 h-4 text-orange-400\" />\n                </div>\n                <div>\n                  <div className=\"font-medium text-[var(--text-primary)]\">Availability</div>\n                  <div className=\"text-sm text-[var(--text-secondary)]\">\n                    9 AM EST - 6 PM IST overlap\n                  </div>\n                </div>\n              </div>\n              \n              {/* Remote Work */}\n              <div className=\"flex items-center gap-3\">\n                <div className=\"p-2 rounded-lg bg-[var(--hover-background)]\">\n                  <Globe className=\"w-4 h-4 text-cyan-400\" />\n                </div>\n                <div>\n                  <div className=\"font-medium text-[var(--text-primary)]\">Remote Work</div>\n                  <div className=\"text-sm text-[var(--text-secondary)]\">\n                    Available for U.S. remote roles\n                  </div>\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n        \n        {/* Social Links & Quick Actions */}\n        <Card hover>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Github className=\"w-5 h-5 text-purple-400\" />\n              Connect & Collaborate\n            </CardTitle>\n            <CardDescription>\n              Explore my work, connect professionally, or reach out directly\n            </CardDescription>\n          </CardHeader>\n          \n          <CardContent>\n            <div className=\"space-y-4\">\n              {/* GitHub */}\n              <Button\n                variant=\"outline\"\n                className=\"w-full justify-start gap-3 h-12\"\n                href=\"https://github.com/dorddis\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n              >\n                <Github className=\"w-5 h-5\" />\n                <div className=\"text-left\">\n                  <div className=\"font-medium\">GitHub</div>\n                  <div className=\"text-xs text-[var(--text-muted)]\">View my repositories and contributions</div>\n                </div>\n              </Button>\n              \n              {/* LinkedIn */}\n              <Button\n                variant=\"outline\"\n                className=\"w-full justify-start gap-3 h-12\"\n                href=\"https://linkedin.com/in/dorddis\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n              >\n                <Linkedin className=\"w-5 h-5\" />\n                <div className=\"text-left\">\n                  <div className=\"font-medium\">LinkedIn</div>\n                  <div className=\"text-xs text-[var(--text-muted)]\">Connect professionally</div>\n                </div>\n              </Button>\n              \n              {/* Email CTA */}\n              <Button\n                className=\"w-full justify-start gap-3 h-12\"\n                href=\"mailto:<EMAIL>?subject=Opportunity Discussion&body=Hi Siddharth,%0D%0A%0D%0AI came across your portfolio and would like to discuss...\"\n              >\n                <Mail className=\"w-5 h-5\" />\n                <div className=\"text-left\">\n                  <div className=\"font-medium\">Send Email</div>\n                  <div className=\"text-xs opacity-90\">Let's discuss opportunities</div>\n                </div>\n              </Button>\n              \n              {/* Phone CTA */}\n              <Button\n                variant=\"outline\"\n                className=\"w-full justify-start gap-3 h-12\"\n                href=\"tel:+919356252711\"\n              >\n                <Phone className=\"w-5 h-5\" />\n                <div className=\"text-left\">\n                  <div className=\"font-medium\">Call Direct</div>\n                  <div className=\"text-xs text-[var(--text-muted)]\">Available during overlap hours</div>\n                </div>\n              </Button>\n            </div>\n            \n            {/* Availability Note */}\n            <div className=\"mt-6 p-4 rounded-lg bg-blue-500/10 border border-blue-500/20\">\n              <div className=\"flex items-start gap-2\">\n                <Clock className=\"w-4 h-4 text-blue-400 mt-0.5\" />\n                <div>\n                  <div className=\"text-sm font-medium text-blue-400\">Timezone Overlap</div>\n                  <div className=\"text-xs text-[var(--text-muted)] mt-1\">\n                    Perfect for U.S. remote work with 9 AM EST - 6 PM IST overlap, \n                    ensuring seamless collaboration during business hours.\n                  </div>\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n      \n      {/* Call to Action */}\n      <div className=\"mt-12 text-center\">\n        <div className=\"max-w-2xl mx-auto\">\n          <h3 className=\"text-xl font-medium text-[var(--text-primary)] mb-4\">\n            Ready to Build Something Amazing Together?\n          </h3>\n          <p className=\"text-[var(--text-muted)] mb-6\">\n            Whether you're looking for a full-stack developer, AI/ML engineer, or cloud specialist, \n            I'm excited to contribute to innovative projects and drive technical excellence.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button\n              size=\"lg\"\n              href=\"mailto:<EMAIL>?subject=Let's Work Together&body=Hi Siddharth,%0D%0A%0D%0AI'm interested in discussing a potential collaboration...\"\n              className=\"px-8\"\n            >\n              <Mail className=\"w-4 h-4 mr-2\" />\n              Start a Conversation\n            </Button>\n            <Button\n              variant=\"outline\"\n              size=\"lg\"\n              href=\"https://linkedin.com/in/dorddis\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"px-8\"\n            >\n              <Linkedin className=\"w-4 h-4 mr-2\" />\n              Connect on LinkedIn\n            </Button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;;;;AAEO,MAAM,UAAoB;IAC/B,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAG,WAAU;0BAAqG;;;;;;0BAGnH,8OAAC;gBAAE,WAAU;0BAAgC;;;;;;0BAK7C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,KAAK;;0CACT,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAA0B;;;;;;;kDAG5C,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAKnB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAyC;;;;;;sEACxD,8OAAC;4DACC,MAAK;4DACL,WAAU;sEACX;;;;;;;;;;;;;;;;;;sDAOL,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAyC;;;;;;sEACxD,8OAAC;4DACC,MAAK;4DACL,WAAU;sEACX;;;;;;;;;;;;;;;;;;sDAOL,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAyC;;;;;;sEACxD,8OAAC;4DAAI,WAAU;sEAAuC;;;;;;;;;;;;;;;;;;sDAK1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAyC;;;;;;sEACxD,8OAAC;4DAAI,WAAU;sEAAuC;;;;;;;;;;;;;;;;;;sDAO1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAyC;;;;;;sEACxD,8OAAC;4DAAI,WAAU;sEAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUhE,8OAAC,gIAAA,CAAA,OAAI;wBAAC,KAAK;;0CACT,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAA4B;;;;;;;kDAGhD,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAKnB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,KAAI;;kEAEJ,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAc;;;;;;0EAC7B,8OAAC;gEAAI,WAAU;0EAAmC;;;;;;;;;;;;;;;;;;0DAKtD,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,KAAI;;kEAEJ,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAc;;;;;;0EAC7B,8OAAC;gEAAI,WAAU;0EAAmC;;;;;;;;;;;;;;;;;;0DAKtD,8OAAC,kIAAA,CAAA,SAAM;gDACL,WAAU;gDACV,MAAK;;kEAEL,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAc;;;;;;0EAC7B,8OAAC;gEAAI,WAAU;0EAAqB;;;;;;;;;;;;;;;;;;0DAKxC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,MAAK;;kEAEL,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAc;;;;;;0EAC7B,8OAAC;gEAAI,WAAU;0EAAmC;;;;;;;;;;;;;;;;;;;;;;;;kDAMxD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAoC;;;;;;sEACnD,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYnE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;sCAI7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,MAAK;oCACL,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGnC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD", "debugId": null}}, {"offset": {"line": 3136, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Projects/Siddharth/portfolio/src/app/page.tsx"], "sourcesContent": ["import { Hero } from '@/components/Hero';\nimport { Experience } from '@/components/Experience';\nimport { Projects } from '@/components/Projects';\nimport { Skills } from '@/components/Skills';\nimport { Education } from '@/components/Education';\nimport { Leadership } from '@/components/Leadership';\nimport { Contact } from '@/components/Contact';\n\nexport default function Home() {\n  return (\n    <main className=\"relative min-h-screen w-full flex flex-col bg-black\">\n      {/* Background Effects */}\n      <div className=\"fixed inset-0\">\n        <div className=\"absolute inset-0 bg-gradient-to-tr from-blue-500/[0.03] via-transparent to-purple-500/[0.03]\"></div>\n        <div className=\"absolute inset-0 bg-[linear-gradient(to_right,#232323_1px,transparent_1px),linear-gradient(to_bottom,#232323_1px,transparent_1px)] bg-[size:44px_44px]\"></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative w-full flex-grow\">\n        <div className=\"max-w-4xl mx-auto px-6\">\n          <Hero />\n\n          <div className=\"max-w-4xl mx-auto\">\n            <Experience />\n            <Projects />\n            <Skills />\n            <Education />\n            <Leadership />\n            <Contact />\n          </div>\n        </div>\n      </div>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAK,WAAU;;0BAEd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0HAAA,CAAA,OAAI;;;;;sCAEL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,aAAU;;;;;8CACX,8OAAC,8HAAA,CAAA,WAAQ;;;;;8CACT,8OAAC,4HAAA,CAAA,SAAM;;;;;8CACP,8OAAC,+HAAA,CAAA,YAAS;;;;;8CACV,8OAAC,gIAAA,CAAA,aAAU;;;;;8CACX,8OAAC,6HAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpB", "debugId": null}}]}